import React, { useState, useEffect } from "react";
import DoctorLayout from "../../components/doctor/DoctorLayout";
import BloodInventoryTable from "../../components/shared/BloodInventoryTable";
import { fetchBloodInventory } from "../../services/bloodInventoryService";
import {
  getBloodComponentName,
  getInventoryStatus,
  mapRhTypeToSymbol,
} from "../../constants/bloodInventoryConstants";
import "../../styles/pages/BloodInventoryViewPage.scss";

const BloodInventoryViewPage = () => {
  const [inventory, setInventory] = useState([]);

  useEffect(() => {
    const loadInventory = async () => {
      try {
        const data = await fetchBloodInventory();
        const inventoryWithStatus = data.map((item) => {
          const bloodType = `${item.bloodGroup}${mapRhTypeToSymbol(
            item.rhType
          )}`;
          const status = getInventoryStatus(item.quantity);
          return {
            ...item,
            bloodType,
            status,
            componentType: getBloodComponentName(item.componentId),
            inventoryId: item.InventoryID || item.inventoryId,
            bagType: item.bagType || "250ml",
          };
        });
        setInventory(inventoryWithStatus);
      } catch (err) {
        console.error("Failed to load inventory:", err);
        setInventory([]);
      }
    };

    loadInventory();
  }, []);

  return (
    <DoctorLayout pageTitle="Kho máu">
      <div className="blood-inventory-view">
        <div className="blood-inventory-view-content no-margin-padding">
          <BloodInventoryTable
            data={inventory}
            showActions={false} // Doctor không có quyền thao tác
            pagination={{ pageSize: 10 }}
            scroll={{ x: true }}
          />
        </div>
      </div>
    </DoctorLayout>
  );
};

export default BloodInventoryViewPage;

import { useState, useEffect, useCallback } from "react";
import { message } from "antd";
import NotificationService from "../services/notificationService";
import authService from "../services/authService";

/**
 * Custom hook for managing notifications
 */
const useNotifications = () => {
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  const [currentUser, setCurrentUser] = useState(null);

  useEffect(() => {
    const user = authService.getCurrentUser();
    setCurrentUser(user);
  }, []);

  /**
   * Load notifications for current user
   */
  const loadNotifications = useCallback(
    async (userId = null) => {
      if (!userId && !currentUser?.id) return;

      setLoading(true);
      try {
        const targetUserId = userId || currentUser.id;
        const data = await NotificationService.getNotifications(targetUserId);

        console.log("Raw notifications data:", data);

        // Filter active notifications
        const activeNotifications =
          NotificationService.filterActiveNotifications(data);
        console.log(
          "Active notifications after filtering:",
          activeNotifications
        );
        setNotifications(activeNotifications);

        // Calculate unread count
        const unread = activeNotifications.filter(
          (n) => !n.isRead && !n.IsRead
        ).length;
        console.log("Unread count:", unread);
        setUnreadCount(unread);
      } catch (error) {
        console.error("Error loading notifications:", error);
        message.error("Không thể tải thông báo!");
        setNotifications([]);
        setUnreadCount(0);
      } finally {
        setLoading(false);
      }
    },
    [currentUser]
  );

  /**
   * Mark notification as read
   */
  const markAsRead = async (notificationId) => {
    try {
      await NotificationService.markAsRead(notificationId);

      // Update local state
      setNotifications((prev) =>
        prev.map((n) =>
          n.id === notificationId || n.notificationId === notificationId
            ? { ...n, isRead: true, IsRead: true }
            : n
        )
      );

      // Update unread count
      setUnreadCount((prev) => Math.max(0, prev - 1));
    } catch (error) {
      console.error("Error marking notification as read:", error);
      message.error("Không thể đánh dấu đã đọc!");
    }
  };

  /**
   * Delete notification
   */
  const deleteNotification = async (notificationId) => {
    try {
      await NotificationService.deleteNotification(notificationId);

      // Remove from local state
      setNotifications((prev) =>
        prev.filter(
          (n) => n.id !== notificationId && n.notificationId !== notificationId
        )
      );

      // Update unread count if it was unread
      const notification = notifications.find(
        (n) => n.id === notificationId || n.notificationId === notificationId
      );
      if (notification && !notification.isRead && !notification.IsRead) {
        setUnreadCount((prev) => Math.max(0, prev - 1));
      }
    } catch (error) {
      console.error("Error deleting notification:", error);
      message.error("Không thể xóa thông báo!");
    }
  };

  /**
   * Create new notification
   */
  const createNotification = async (notificationData) => {
    try {
      const newNotification = await NotificationService.createNotification(
        notificationData
      );

      // Add to local state
      setNotifications((prev) => [newNotification, ...prev]);

      // Update unread count
      setUnreadCount((prev) => prev + 1);

      return newNotification;
    } catch (error) {
      console.error("Error creating notification:", error);
      message.error("Không thể tạo thông báo!");
      throw error;
    }
  };

  /**
   * Refresh notifications (useful after creating reminders)
   */
  const refreshNotifications = useCallback(async () => {
    await loadNotifications();
  }, [loadNotifications]);

  /**
   * Get notifications by type
   */
  const getNotificationsByType = useCallback(
    (type) => {
      return notifications.filter((n) => n.type === type);
    },
    [notifications]
  );

  /**
   * Get unread notifications
   */
  const getUnreadNotifications = useCallback(() => {
    return notifications.filter((n) => !n.isRead && !n.IsRead);
  }, [notifications]);

  /**
   * Mark all notifications as read
   */
  const markAllAsRead = async () => {
    try {
      const unreadNotifications = getUnreadNotifications();

      // Mark all unread notifications as read
      for (const notification of unreadNotifications) {
        await NotificationService.markAsRead(
          notification.id || notification.notificationId
        );
      }

      // Update local state
      setNotifications((prev) =>
        prev.map((n) => ({ ...n, isRead: true, IsRead: true }))
      );

      setUnreadCount(0);
      message.success("Đã đánh dấu tất cả thông báo là đã đọc!");
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
      message.error("Không thể đánh dấu tất cả thông báo!");
    }
  };

  // Auto-load notifications when currentUser changes
  useEffect(() => {
    if (currentUser?.id) {
      loadNotifications();
    }
  }, [currentUser, loadNotifications]);

  // Auto-refresh notifications every 30 seconds
  useEffect(() => {
    if (!currentUser?.id) return;

    const interval = setInterval(() => {
      loadNotifications();
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [currentUser, loadNotifications]);

  // Real-time notifications disabled - reminders only stored in database

  return {
    // Data
    notifications,
    loading,
    unreadCount,
    currentUser,

    // Actions
    loadNotifications,
    markAsRead,
    deleteNotification,
    createNotification,
    refreshNotifications,
    markAllAsRead,

    // Utilities
    getNotificationsByType,
    getUnreadNotifications,
  };
};

export default useNotifications;

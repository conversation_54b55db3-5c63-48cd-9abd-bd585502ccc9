import { Table, Card, Button, Tooltip, Space, Tag } from "antd";
import { EyeOutlined, ClockCircleOutlined } from "@ant-design/icons";
import FilterSection from "./FilterSection";
import { 
  formatDate, 
  getNotificationStatusInfo,
  BLOOD_TYPE_OPTIONS,
  SCHEDULE_SORT_OPTIONS 
} from "../../../utils/donationScheduleHelpers";

/**
 * Schedule tab component for registered donations
 */
const ScheduleTab = ({
  donations,
  filters,
  onFilterChange,
  scheduleSort,
  onSortChange,
  loading,
  onViewDetails,
  onViewWorkflow,
}) => {
  const columns = [
    {
      title: "Ngày hẹn",
      dataIndex: "appointmentDate",
      key: "appointmentDate",
      width: 130,
      sorter: true,
      render: (date) => formatDate(date),
    },
    {
      title: "Nhóm máu",
      dataIndex: "bloodType",
      key: "bloodType",
      width: 100,
      align: "center",
      sorter: true,
      render: (bloodType) => (
        <Tag color="#D93E4C" style={{ fontWeight: "bold" }}>
          {bloodType}
        </Tag>
      ),
    },
    {
      title: "<PERSON><PERSON>ợng máu dự kiến",
      dataIndex: "expectedQuantity",
      key: "expectedQuantity",
      width: 150,
      align: "center",
      sorter: true,
      render: (quantity) => (
        <span style={{ fontWeight: 600, color: "#20374E" }}>
          {quantity}
        </span>
      ),
    },
    {
      title: "Tên người hiến",
      dataIndex: "donorName",
      key: "donorName",
      width: 180,
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 600 }}>{text}</div>
          <div style={{ fontSize: "0.8rem", color: "#666" }}>
            ID: {record.donorId}
          </div>
        </div>
      ),
    },
    {
      title: "Trạng thái thông báo",
      dataIndex: "notificationStatus",
      key: "notificationStatus",
      width: 160,
      align: "center",
      render: (status, record) => {
        const statusInfo = getNotificationStatusInfo(status);
        return (
          <div>
            <Tag
              color={statusInfo.color}
              style={{ marginBottom: 4 }}
            >
              {statusInfo.text}
            </Tag>
            {status === "sent" && record.notificationSentAt && (
              <div style={{ fontSize: "0.7rem", color: "#666" }}>
                {formatDate(record.notificationSentAt)}
              </div>
            )}
          </div>
        );
      },
    },
    {
      title: "Hành động",
      key: "actions",
      width: 150,
      render: (_, record) => (
        <Space>
          <Tooltip title="Xem chi tiết">
            <Button
              type="primary"
              icon={<EyeOutlined />}
              size="small"
              onClick={() => onViewDetails(record)}
            />
          </Tooltip>
          <Tooltip title="Quy trình hiến máu">
            <Button
              type="default"
              icon={<ClockCircleOutlined />}
              size="small"
              onClick={() => onViewWorkflow(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const filterOptions = {
    bloodType: {
      label: "Nhóm máu:",
      options: BLOOD_TYPE_OPTIONS,
    },
    sort: {
      label: "Sắp xếp theo:",
      options: SCHEDULE_SORT_OPTIONS,
    },
  };

  return (
    <div className="schedule-tab-content">
      <FilterSection
        filters={filters}
        onFilterChange={onFilterChange}
        sortValue={`${scheduleSort.field}-${scheduleSort.order}`}
        onSortChange={(value) => {
          const [field, order] = value.split("-");
          onSortChange({ field, order });
        }}
        filterOptions={filterOptions}
      />

      <Card className="donations-table-card">
        <Table
          dataSource={donations}
          columns={columns}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} của ${total} lịch hẹn`,
          }}
          scroll={{ x: 800 }}
          onChange={(_, __, sorter) => {
            if (sorter.field) {
              onSortChange({
                field: sorter.field,
                order: sorter.order === "ascend" ? "asc" : "desc",
              });
            }
          }}
        />
      </Card>
    </div>
  );
};

export default ScheduleTab;

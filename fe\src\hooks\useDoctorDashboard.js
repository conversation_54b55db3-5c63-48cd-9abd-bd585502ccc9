import { useState, useEffect, useCallback } from 'react';
import doctorDashboardService from '../services/doctorDashboardService';
import { fetchBloodInventory } from '../services/bloodInventoryService';
import { bloodRequestService } from '../services/bloodRequestService';
import authService from '../services/authService';
import { DOCTOR_TYPES } from '../services/mockData';

/**
 * Custom hook for Doctor Dashboard data management
 */
const useDoctorDashboard = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dashboardData, setDashboardData] = useState({
    statistics: {
      totalRequests: 0,
      pendingRequests: 0,
      approvedRequests: 0,
      completedRequests: 0,
      urgentNotifications: 0,
    },
    bloodInventory: [],
    monthlyTrend: [],
    recentRequests: [],
    notifications: [],
  });

  const currentUser = authService.getCurrentUser();
  const isBloodDepartment = currentUser?.doctorType === DOCTOR_TYPES.BLOOD_DEPARTMENT;

  /**
   * Load dashboard statistics
   */
  const loadDashboardStats = useCallback(async () => {
    try {
      const doctorId = currentUser?.id;
      if (!doctorId) return;

      // Get blood request statistics
      const requestsResponse = await bloodRequestService.getBloodRequestsByDoctor(doctorId);
      
      if (requestsResponse.success) {
        const requests = requestsResponse.data || [];
        
        const statistics = {
          totalRequests: requests.length,
          pendingRequests: requests.filter(r => r.status === 0).length,
          approvedRequests: requests.filter(r => r.status === 1).length,
          completedRequests: requests.filter(r => r.status === 2).length,
          urgentNotifications: 0, // Will be updated from notifications
        };

        setDashboardData(prev => ({
          ...prev,
          statistics,
          recentRequests: requests.slice(0, 5), // Get 5 most recent
        }));
      }
    } catch (err) {
      console.error('Error loading dashboard stats:', err);
      setError('Không thể tải thống kê dashboard');
    }
  }, [currentUser?.id]);

  /**
   * Load blood inventory data
   */
  const loadBloodInventory = useCallback(async () => {
    try {
      const inventoryData = await fetchBloodInventory();
      
      // Transform data for charts
      const bloodGroupData = inventoryData.reduce((acc, item) => {
        const bloodType = `${item.bloodGroup}${item.rhType === 'Rh+' ? '+' : '-'}`;
        const existing = acc.find(group => group.name === bloodType);
        
        if (existing) {
          existing.value += item.quantity;
        } else {
          acc.push({
            name: bloodType,
            value: item.quantity,
            status: item.status,
          });
        }
        
        return acc;
      }, []);

      setDashboardData(prev => ({
        ...prev,
        bloodInventory: bloodGroupData,
      }));
    } catch (err) {
      console.error('Error loading blood inventory:', err);
      setError('Không thể tải dữ liệu kho máu');
    }
  }, []);

  /**
   * Load monthly requests trend
   */
  const loadMonthlyTrend = useCallback(async () => {
    try {
      // For now, use mock data until API is available
      const mockTrendData = [
        { month: "T7", requests: 12 },
        { month: "T8", requests: 15 },
        { month: "T9", requests: 10 },
        { month: "T10", requests: 18 },
        { month: "T11", requests: 14 },
        { month: "T12", requests: 20 },
      ];

      setDashboardData(prev => ({
        ...prev,
        monthlyTrend: mockTrendData,
      }));
    } catch (err) {
      console.error('Error loading monthly trend:', err);
      setError('Không thể tải xu hướng theo tháng');
    }
  }, []);

  /**
   * Load notifications
   */
  const loadNotifications = useCallback(async () => {
    try {
      // Mock notifications for now
      const mockNotifications = [
        {
          id: 1,
          type: "emergency",
          title: "Thiếu máu O- khẩn cấp",
          message: "Kho máu O- chỉ còn 2 đơn vị, cần bổ sung ngay",
          isRead: false,
          createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        },
        {
          id: 2,
          type: "info",
          title: "Yêu cầu máu mới",
          message: "Có 3 yêu cầu máu mới cần xử lý",
          isRead: false,
          createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        },
        {
          id: 3,
          type: "warning",
          title: "Sắp hết hạn",
          message: "5 đơn vị máu A+ sẽ hết hạn trong 3 ngày",
          isRead: true,
          createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        },
      ];

      const urgentCount = mockNotifications.filter(
        n => n.type === "emergency" && !n.isRead
      ).length;

      setDashboardData(prev => ({
        ...prev,
        notifications: mockNotifications,
        statistics: {
          ...prev.statistics,
          urgentNotifications: urgentCount,
        },
      }));
    } catch (err) {
      console.error('Error loading notifications:', err);
      setError('Không thể tải thông báo');
    }
  }, []);

  /**
   * Mark notification as read
   */
  const markNotificationAsRead = useCallback(async (notificationId) => {
    try {
      setDashboardData(prev => ({
        ...prev,
        notifications: prev.notifications.map(notification =>
          notification.id === notificationId
            ? { ...notification, isRead: true }
            : notification
        ),
      }));

      // Update urgent notifications count
      setDashboardData(prev => ({
        ...prev,
        statistics: {
          ...prev.statistics,
          urgentNotifications: prev.notifications.filter(
            n => n.type === "emergency" && !n.isRead && n.id !== notificationId
          ).length,
        },
      }));
    } catch (err) {
      console.error('Error marking notification as read:', err);
    }
  }, []);

  /**
   * Refresh all dashboard data
   */
  const refreshDashboard = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      await Promise.all([
        loadDashboardStats(),
        loadBloodInventory(),
        loadMonthlyTrend(),
        loadNotifications(),
      ]);
    } catch (err) {
      console.error('Error refreshing dashboard:', err);
      setError('Không thể tải dữ liệu dashboard');
    } finally {
      setLoading(false);
    }
  }, [loadDashboardStats, loadBloodInventory, loadMonthlyTrend, loadNotifications]);

  // Load data on mount
  useEffect(() => {
    refreshDashboard();
  }, [refreshDashboard]);

  return {
    loading,
    error,
    dashboardData,
    isBloodDepartment,
    currentUser,
    refreshDashboard,
    markNotificationAsRead,
  };
};

export default useDoctorDashboard;

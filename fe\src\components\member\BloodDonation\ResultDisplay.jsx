import React from "react";
import { useNavigate } from "react-router-dom";
import {
  CheckOutlined,
  CloseOutlined,
  RedoOutlined,
} from "@ant-design/icons";

/**
 * Component hiển thị kết quả đăng ký hiến máu
 */
const ResultDisplay = ({
  registrationResult,
  appointmentData,
  getTimeSlotText,
  onRetry,
}) => {
  const navigate = useNavigate();

  if (!registrationResult) return null;

  return (
    <div className="blood-donation-form-page">
      <div className="registration-content">
        <div className="result-section">
          <div className={`result-card ${registrationResult.status}`}>
            <div className="result-icon">
              {registrationResult.status === "failed" ||
              registrationResult.status === "error" ? (
                <CloseOutlined />
              ) : (
                <CheckOutlined />
              )}
            </div>
            <div className="result-content">
              <h2>{registrationResult.message}</h2>
              <p>{registrationResult.description}</p>

              {registrationResult.status === "scheduled" && (
                <div className="appointment-summary">
                  <h3>Thông tin lịch hẹn</h3>
                  <div className="appointment-details">
                    <div className="detail-item">
                      <strong>Ngày:</strong>{" "}
                      {new Date(
                        appointmentData.preferredDate
                      ).toLocaleDateString("vi-VN")}
                    </div>
                    <div className="detail-item">
                      <strong>Khung giờ:</strong>{" "}
                      {getTimeSlotText(appointmentData.timeSlot)}
                    </div>
                    <div className="detail-item">
                      <strong>Địa điểm:</strong> Bệnh viện Đa khoa Ánh Dương -
                      Khoa Huyết học, Tầng 2
                    </div>
                    <div className="detail-item">
                      <strong>Địa chỉ:</strong> Đường Cách Mạng Tháng 8, Quận
                      3, TP.HCM, Vietnam
                    </div>
                  </div>
                </div>
              )}

              <div className="result-actions">
                <button
                  className="btn btn-primary"
                  onClick={() => navigate("/member/")}
                >
                  Về trang chủ
                </button>

                {(registrationResult.status === "failed" ||
                  registrationResult.status === "error") && (
                  <button
                    className="btn btn-outline-primary retry-button"
                    onClick={onRetry}
                  >
                    <RedoOutlined style={{ marginRight: "8px" }} />
                    Thử lại
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResultDisplay;

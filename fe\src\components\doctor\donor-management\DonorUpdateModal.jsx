import React from "react";
import {
  <PERSON><PERSON>, <PERSON>ton, Card, Avatar, Space, Divider, Row, Col,
  Select, InputNumber, Input
} from "antd";
import {
  EditOutlined, UserOutlined, PhoneOutlined, MailOutlined
} from "@ant-design/icons";

const { TextArea } = Input;

/**
 * Modal cập nhật thông tin người hiến máu
 */
const DonorUpdateModal = ({
  visible,
  onCancel,
  onSave,
  selectedDonor,
  updateData,
  setUpdateData
}) => {

  const getTimeSlotText = (slot) => {
    if (slot === "morning" || slot === "Sáng (7:00-12:00)") {
      return "7:00 - 12:00";
    } else if (slot === "afternoon" || slot === "Chiều (13:00-17:00)") {
      return "13:00 - 17:00";
    }
    return slot || "N/A";
  };

  if (!selectedDonor) return null;

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <EditOutlined style={{ color: '#1890ff' }} />
          <span>Cập nhật thông tin người hiến máu</span>
        </div>
      }
      open={visible}
      onCancel={onCancel}
      width={800}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          Hủy
        </Button>,
        <Button key="save" type="primary" onClick={onSave}>
          <EditOutlined /> Lưu thay đổi
        </Button>
      ]}
    >
      {/* Donor Summary Card */}
      <Card
        size="small"
        style={{ marginBottom: 16, backgroundColor: '#f8f9fa' }}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          <Avatar size={64} icon={<UserOutlined />} style={{ backgroundColor: '#1890ff' }} />
          <div>
            <h3 style={{ margin: 0, color: '#1890ff' }}>{selectedDonor.name}</h3>
            <Space direction="vertical" size={4}>
              <div><PhoneOutlined /> {selectedDonor.phone}</div>
              <div><MailOutlined /> {selectedDonor.email}</div>
              <div>
                📅 {new Date(selectedDonor.appointmentDate).toLocaleDateString("vi-VN")}
                - {getTimeSlotText(selectedDonor.timeSlot)}
              </div>
            </Space>
          </div>
        </div>
      </Card>

      <Divider orientation="left">🩸 Thông tin máu</Divider>
      <Row gutter={16}>
        <Col span={12}>
          <div style={{ marginBottom: 16 }}>
            <label style={{ display: 'block', marginBottom: 4, fontWeight: 'bold' }}>
              Nhóm máu:
            </label>
            <Select
              style={{ width: '100%' }}
              value={updateData.bloodType}
              onChange={(value) =>
                setUpdateData((prev) => ({
                  ...prev,
                  bloodType: value,
                }))
              }
              placeholder="Chọn nhóm máu"
            >
              <Select.Option value="O+">O+</Select.Option>
              <Select.Option value="O-">O-</Select.Option>
              <Select.Option value="A+">A+</Select.Option>
              <Select.Option value="A-">A-</Select.Option>
              <Select.Option value="B+">B+</Select.Option>
              <Select.Option value="B-">B-</Select.Option>
              <Select.Option value="AB+">AB+</Select.Option>
              <Select.Option value="AB-">AB-</Select.Option>
            </Select>
          </div>
        </Col>
        <Col span={12}>
          <div style={{ marginBottom: 16 }}>
            <label style={{ display: 'block', marginBottom: 4, fontWeight: 'bold' }}>
              Tình trạng sức khỏe:
            </label>
            <Select
              style={{ width: '100%' }}
              value={updateData.healthStatus}
              onChange={(value) =>
                setUpdateData((prev) => ({
                  ...prev,
                  healthStatus: value,
                }))
              }
              placeholder="Chọn tình trạng"
            >
              <Select.Option value="excellent">Xuất sắc</Select.Option>
              <Select.Option value="good">Tốt</Select.Option>
              <Select.Option value="fair">Khá</Select.Option>
              <Select.Option value="poor">Kém</Select.Option>
            </Select>
          </div>
        </Col>
      </Row>

      <Divider orientation="left">🔬 Kết quả xét nghiệm</Divider>
      <Row gutter={16}>
        <Col span={12}>
          <div style={{ marginBottom: 16 }}>
            <label style={{ display: 'block', marginBottom: 4, fontWeight: 'bold' }}>
              Hemoglobin (g/dL):
            </label>
            <InputNumber
              style={{ width: '100%' }}
              step={0.1}
              value={updateData.testResults.hemoglobin}
              onChange={(value) =>
                setUpdateData((prev) => ({
                  ...prev,
                  testResults: {
                    ...prev.testResults,
                    hemoglobin: value,
                  },
                }))
              }
              placeholder="VD: 14.5"
            />
          </div>
        </Col>
        <Col span={12}>
          <div style={{ marginBottom: 16 }}>
            <label style={{ display: 'block', marginBottom: 4, fontWeight: 'bold' }}>
              Huyết áp (mmHg):
            </label>
            <Input
              value={updateData.testResults.bloodPressure}
              onChange={(e) =>
                setUpdateData((prev) => ({
                  ...prev,
                  testResults: {
                    ...prev.testResults,
                    bloodPressure: e.target.value,
                  },
                }))
              }
              placeholder="VD: 120/80"
            />
          </div>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <div style={{ marginBottom: 16 }}>
            <label style={{ display: 'block', marginBottom: 4, fontWeight: 'bold' }}>
              Nhịp tim (bpm):
            </label>
            <InputNumber
              style={{ width: '100%' }}
              value={updateData.testResults.heartRate}
              onChange={(value) =>
                setUpdateData((prev) => ({
                  ...prev,
                  testResults: {
                    ...prev.testResults,
                    heartRate: value,
                  },
                }))
              }
              placeholder="VD: 72"
            />
          </div>
        </Col>
        <Col span={12}>
          <div style={{ marginBottom: 16 }}>
            <label style={{ display: 'block', marginBottom: 4, fontWeight: 'bold' }}>
              Nhiệt độ (°C):
            </label>
            <InputNumber
              style={{ width: '100%' }}
              step={0.1}
              value={updateData.testResults.temperature}
              onChange={(value) =>
                setUpdateData((prev) => ({
                  ...prev,
                  testResults: {
                    ...prev.testResults,
                    temperature: value,
                  },
                }))
              }
              placeholder="VD: 36.5"
            />
          </div>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <div style={{ marginBottom: 16 }}>
            <label style={{ display: 'block', marginBottom: 4, fontWeight: 'bold' }}>
              Chiều cao (cm):
            </label>
            <InputNumber
              style={{ width: '100%' }}
              value={updateData.testResults.height}
              onChange={(value) =>
                setUpdateData((prev) => ({
                  ...prev,
                  testResults: {
                    ...prev.testResults,
                    height: value,
                  },
                }))
              }
              placeholder="VD: 170"
            />
          </div>
        </Col>
      </Row>

      <Divider orientation="left">📝 Ghi chú</Divider>
      <TextArea
        rows={4}
        value={updateData.notes}
        onChange={(e) =>
          setUpdateData((prev) => ({
            ...prev,
            notes: e.target.value,
          }))
        }
        placeholder="Nhập ghi chú về tình trạng sức khỏe, kết quả khám..."
      />
    </Modal>
  );
};

export default DonorUpdateModal;

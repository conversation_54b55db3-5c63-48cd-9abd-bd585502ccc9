import React from "react";
import {
  Form,
  DatePicker,
  Radio,
  Button,
  Card,
  Row,
  Col,
  Space,
  Typography,
  Alert,
  Divider,
} from "antd";
import {
  CalendarOutlined,
  ClockCircleOutlined,
  ArrowLeftOutlined,
  CheckOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";

const { Title, Text } = Typography;

/**
 * Component hiển thị bước 3: Đặt lịch hẹn
 */
const AppointmentStep = ({
  appointmentData,
  distanceInfo,
  loading,
  onAppointmentChange,
  onSubmit,
  onBack,
  getTimeSlotText,
}) => {
  const handleDateChange = (date) => {
    onAppointmentChange("preferredDate", date ? date.format("YYYY-MM-DD") : "");
  };

  const handleTimeSlotChange = (e) => {
    onAppointmentChange("timeSlot", e.target.value);
  };

  return (
    <Card
      title={
        <Space>
          <CalendarOutlined />
          <span>Đặt lịch hẹn hiến máu</span>
        </Space>
      }
      className="appointment-card"
    >
      <Alert
        message="Chúc mừng! Bạn đủ điều kiện hiến máu"
        description="Vui lòng chọn ngày và giờ phù hợp để đặt lịch hẹn hiến máu."
        type="success"
        icon={<CheckOutlined />}
        className="success-alert"
        showIcon
      />

      <Form layout="vertical">
        <Title level={4} className="section-title">
          Thông tin lịch hẹn
        </Title>

        <Row gutter={24}>
          <Col xs={24} md={12}>
            <Form.Item
              label={
                <span className="form-label">
                  <CalendarOutlined style={{ marginRight: 8 }} />
                  Chọn ngày hiến máu
                </span>
              }
              required
            >
              <DatePicker
                value={
                  appointmentData.preferredDate
                    ? dayjs(appointmentData.preferredDate)
                    : null
                }
                onChange={handleDateChange}
                disabledDate={(current) => {
                  // Không cho chọn ngày trong quá khứ
                  return current && current < dayjs().startOf("day");
                }}
                className="datepicker-full"
                placeholder="Chọn ngày hiến máu"
                format="DD/MM/YYYY"
                size="large"
              />
            </Form.Item>
          </Col>

          <Col xs={24} md={12}>
            <Form.Item
              label={
                <span className="form-label">
                  <ClockCircleOutlined style={{ marginRight: 8 }} />
                  Chọn khung giờ
                </span>
              }
              required
            >
              <Radio.Group
                value={appointmentData.timeSlot}
                onChange={handleTimeSlotChange}
                className="time-slot-group"
                size="large"
              >
                <Radio.Button value="morning" className="time-slot-option">
                  <div className="time-slot-content">
                    <div className="time-slot-title">Buổi sáng</div>
                    <div className="time-slot-time">7:00 - 12:00</div>
                  </div>
                </Radio.Button>
                <Radio.Button value="afternoon" className="time-slot-option">
                  <div className="time-slot-content">
                    <div className="time-slot-title">Buổi chiều</div>
                    <div className="time-slot-time">13:00 - 17:00</div>
                  </div>
                </Radio.Button>
              </Radio.Group>
            </Form.Item>
          </Col>
        </Row>

        <Divider />

        <Title level={4} className="section-title">
          Địa điểm hiến máu
        </Title>

        <div className="location-info">
          <div className="location-card">
            <div className="location-header">
              <Title level={5} className="location-name">
                Bệnh viện Đa khoa Ánh Dương
              </Title>
              <Text className="location-department">
                Khoa Huyết học - Tầng 2
              </Text>
            </div>
            <div className="location-details">
              <div className="location-address">
                <Text strong>Địa chỉ: </Text>
                <Text>Đường Cách Mạng Tháng 8, Quận 3, TP.HCM, Vietnam</Text>
              </div>
              {distanceInfo && (
                <div className="location-distance">
                  <Text strong>Khoảng cách: </Text>
                  <Text>{distanceInfo.formattedDistance}</Text>
                </div>
              )}
            </div>
          </div>
        </div>

        <Divider />

        {/* Appointment Summary */}
        {appointmentData.preferredDate && appointmentData.timeSlot && (
          <div className="appointment-summary">
            <Title level={4} className="section-title">
              Tóm tắt lịch hẹn
            </Title>
            <div className="summary-card">
              <Row gutter={16}>
                <Col xs={24} md={12}>
                  <div className="summary-item">
                    <Text strong>Ngày hiến máu:</Text>
                    <br />
                    <Text className="summary-value">
                      {dayjs(appointmentData.preferredDate).format("dddd, DD/MM/YYYY")}
                    </Text>
                  </div>
                </Col>
                <Col xs={24} md={12}>
                  <div className="summary-item">
                    <Text strong>Khung giờ:</Text>
                    <br />
                    <Text className="summary-value">
                      {getTimeSlotText(appointmentData.timeSlot)}
                    </Text>
                  </div>
                </Col>
              </Row>
              <div className="summary-item">
                <Text strong>Địa điểm:</Text>
                <br />
                <Text className="summary-value">
                  Bệnh viện Đa khoa Ánh Dương - Khoa Huyết học, Tầng 2
                </Text>
                <br />
                <Text type="secondary">
                  Đường Cách Mạng Tháng 8, Quận 3, TP.HCM, Vietnam
                </Text>
              </div>
            </div>
          </div>
        )}

        {/* Navigation Buttons */}
        <div className="form-navigation">
          <Button
            size="large"
            onClick={onBack}
            icon={<ArrowLeftOutlined />}
            className="back-button"
          >
            Quay lại
          </Button>
          <Button
            type="primary"
            size="large"
            loading={loading}
            onClick={onSubmit}
            className="submit-button"
            disabled={!appointmentData.preferredDate || !appointmentData.timeSlot}
          >
            {loading ? "Đang đặt lịch..." : "Xác nhận đặt lịch"}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default AppointmentStep;

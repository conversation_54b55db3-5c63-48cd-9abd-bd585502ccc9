/**
 * Utility functions cho blood donation form
 */

/**
 * Tính tuổi từ ngày sinh
 * @param {string} dateOfBirth - Ng<PERSON><PERSON> sinh (YYYY-MM-DD)
 * @returns {number} - Tuổi
 */
export const calculateAge = (dateOfBirth) => {
  const today = new Date();
  const birthDate = new Date(dateOfBirth);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();

  if (
    monthDiff < 0 ||
    (monthDiff === 0 && today.getDate() < birthDate.getDate())
  ) {
    age--;
  }

  return age;
};

/**
 * Kiểm tra điều kiện đủ tuổi hiến máu
 * @param {string} dateOfBirth - Ngày sinh
 * @returns {boolean} - True nếu đủ tuổi (18-60)
 */
export const isEligibleAge = (dateOfBirth) => {
  const age = calculateAge(dateOfBirth);
  return age >= 18 && age <= 60;
};

/**
 * <PERSON>ể<PERSON> tra cân nặng tối thiểu theo giới tính
 * @param {number} weight - Cân nặng
 * @param {string} gender - Giới tính ('male', 'female')
 * @returns {boolean} - True nếu đạt cân nặng tối thiểu
 */
export const isEligibleWeight = (weight, gender) => {
  const minWeight = gender === "female" ? 42 : 45;
  return parseFloat(weight) >= minWeight;
};

/**
 * Lấy cân nặng tối thiểu theo giới tính
 * @param {string} gender - Giới tính ('male', 'female')
 * @returns {number} - Cân nặng tối thiểu
 */
export const getMinWeight = (gender) => {
  return gender === "female" ? 42 : 45;
};

/**
 * Kiểm tra điều kiện đủ để hiến máu
 * @param {Object} personalInfo - Thông tin cá nhân
 * @param {Object} healthSurvey - Khảo sát sức khỏe
 * @returns {Object} - {eligible: boolean, reason: string}
 */
export const checkEligibility = (personalInfo, healthSurvey) => {
  const {
    weight,
    hasCurrentMedicalConditions,
    hasPreviousSeriousConditions,
    otherPreviousConditions,
    hadMalariaSyphilisTuberculosis,
    hadBloodTransfusion,
    hadVaccination,
    last12MonthsNone,
    hadTyphoidSepsis,
    unexplainedWeightLoss,
    persistentLymphNodes,
    invasiveMedicalProcedures,
    tattoosPiercings,
    drugUse,
    bloodExposure,
    livedWithHepatitisB,
    sexualContactWithInfected,
    sameSexContact,
    last6MonthsNone,
    hadUrinaryInfection,
    visitedEpidemicArea,
    last1MonthNone,
    hadFluSymptoms,
    last14DaysNone,
    otherSymptoms,
    tookAntibiotics,
    last7DaysNone,
    otherMedications,
    isPregnantOrNursing,
    hadPregnancyTermination,
    womenQuestionsNone,
  } = healthSurvey;

  // Age check
  const age = calculateAge(personalInfo.dateOfBirth);
  if (age < 18 || age > 60) {
    return {
      eligible: false,
      reason: "Tuổi không đủ điều kiện (18-60 tuổi)",
    };
  }

  // Weight check based on gender
  const minWeight = personalInfo.gender === "female" ? 42 : 45;
  if (parseFloat(weight) < minWeight) {
    return { eligible: false, reason: `Cân nặng dưới ${minWeight}kg` };
  }

  // Question 2: Current Medical Conditions
  if (hasCurrentMedicalConditions === true) {
    return { eligible: false, reason: "Có bệnh lý hiện tại" };
  }

  // Question 3: Previous Serious Conditions
  if (
    hasPreviousSeriousConditions === true ||
    hasPreviousSeriousConditions === "other"
  ) {
    return { eligible: false, reason: "Có tiền sử bệnh nghiêm trọng" };
  }

  // Question 4: Last 12 Months
  if (
    !last12MonthsNone &&
    (hadMalariaSyphilisTuberculosis || hadBloodTransfusion || hadVaccination)
  ) {
    return { eligible: false, reason: "Có yếu tố rủi ro trong 12 tháng qua" };
  }

  // Question 5: Last 6 Months
  if (
    !last6MonthsNone &&
    (hadTyphoidSepsis ||
      unexplainedWeightLoss ||
      persistentLymphNodes ||
      invasiveMedicalProcedures ||
      tattoosPiercings ||
      drugUse ||
      bloodExposure ||
      livedWithHepatitisB ||
      sexualContactWithInfected ||
      sameSexContact)
  ) {
    return { eligible: false, reason: "Có yếu tố rủi ro trong 6 tháng qua" };
  }

  // Question 6: Last 1 Month
  if (!last1MonthNone && (hadUrinaryInfection || visitedEpidemicArea)) {
    return { eligible: false, reason: "Có yếu tố rủi ro trong 1 tháng qua" };
  }

  // Question 7: Last 14 Days
  if (!last14DaysNone && (hadFluSymptoms || otherSymptoms)) {
    return {
      eligible: false,
      reason: "Có triệu chứng bệnh trong 14 ngày qua",
    };
  }

  // Question 8: Last 7 Days
  if (!last7DaysNone && (tookAntibiotics || otherMedications)) {
    return { eligible: false, reason: "Đã sử dụng thuốc trong 7 ngày qua" };
  }

  // Question 9: Women Only
  if (
    personalInfo.gender === "female" &&
    !womenQuestionsNone &&
    (isPregnantOrNursing || hadPregnancyTermination)
  ) {
    return { eligible: false, reason: "Không đủ điều kiện về thai sản" };
  }

  return { eligible: true, reason: "" };
};

/**
 * Validate thông tin cá nhân
 * @param {Object} personalInfo - Thông tin cá nhân
 * @returns {Object} - {isValid: boolean, errors: string[]}
 */
export const validatePersonalInfo = (personalInfo) => {
  const errors = [];

  if (!personalInfo.fullName?.trim()) {
    errors.push("Vui lòng nhập họ và tên");
  }

  if (!personalInfo.phone?.trim()) {
    errors.push("Vui lòng nhập số điện thoại");
  }

  if (!personalInfo.dateOfBirth) {
    errors.push("Vui lòng chọn ngày sinh");
  }

  if (!personalInfo.gender) {
    errors.push("Vui lòng chọn giới tính");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Format blood type từ bloodGroup và rhType
 * @param {string} bloodGroup - Nhóm máu (A, B, AB, O)
 * @param {string} rhType - Rh type (Rh+, Rh-, +, -)
 * @returns {string} - Blood type đã format (A+, B-, etc.)
 */
export const formatBloodType = (bloodGroup, rhType) => {
  if (!bloodGroup || !rhType) return "";
  
  // Chuẩn hóa ký hiệu Rh
  const rhSymbol =
    rhType === "Rh+" || rhType === "+"
      ? "+"
      : rhType === "Rh-" || rhType === "-"
      ? "-"
      : rhType;
  
  return `${bloodGroup}${rhSymbol}`;
};

/**
 * Tạo địa chỉ đầy đủ từ các thành phần
 * @param {Object} addressComponents - Các thành phần địa chỉ
 * @returns {string} - Địa chỉ đầy đủ
 */
export const createFullAddress = (addressComponents) => {
  const {
    address,
    houseNumber,
    street,
    wardName,
    districtName,
    provinceName,
  } = addressComponents;

  return [
    address || `${houseNumber} ${street}`.trim(),
    wardName,
    districtName,
    provinceName,
  ]
    .filter(Boolean)
    .join(", ");
};

/**
 * Validate appointment data
 * @param {Object} appointmentData - Dữ liệu lịch hẹn
 * @param {Object} healthSurvey - Khảo sát sức khỏe
 * @returns {Object} - {isValid: boolean, errors: string[]}
 */
export const validateAppointmentData = (appointmentData, healthSurvey) => {
  const errors = [];

  if (!appointmentData.preferredDate) {
    errors.push("Vui lòng chọn ngày đặt lịch");
  }

  if (!appointmentData.timeSlot) {
    errors.push("Vui lòng chọn khung giờ đặt lịch");
  }

  if (!healthSurvey.weight) {
    errors.push("Vui lòng nhập cân nặng");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

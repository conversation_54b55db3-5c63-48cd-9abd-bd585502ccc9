import {
  DONATION_STATUS,
  STATUS_TRANSITIONS,
  USER_ROLES,
  WORKFLOW_PERMISSIONS
} from "../constants/systemConstants";
import { DOCTOR_TYPES } from "../services/mockData";

// StatusWorkflowService helper
export const StatusWorkflowService = {
  // New status constants based on database TINYINT values
  DONATION_STATUSES: {
    PENDING: 0,        // Đang chờ duyệt
    REJECTED: 1,       // Không chấp nhận
    APPROVED: 2,       // Chấp nhận
    CANCELLED: 3,      // Hủy
    // Keep old constants for backward compatibility
    ...DONATION_STATUS
  },
  USER_ROLES: USER_ROLES,
  DOCTOR_TYPES: DOCTOR_TYPES,

  getStatusInfo: (status, type = 'donation') => {
    // Handle both numeric and string status values
    const numericStatus = typeof status === 'string' ? parseInt(status) : status;

    const statusMap = {
      // New numeric statuses
      0: {
        text: "Đang chờ duyệt",
        color: "#faad14"
      },
      1: {
        text: "Không chấp nhận",
        color: "#ff4d4f"
      },
      2: {
        text: "Chấp nhận",
        color: "#52c41a"
      },
      3: {
        text: "Hủy",
        color: "#d9d9d9"
      },
      // Legacy string statuses for backward compatibility
      [DONATION_STATUS.REGISTERED]: {
        text: "Đã đăng ký",
        color: "#1890ff"
      },
      [DONATION_STATUS.HEALTH_CHECKED]: {
        text: "Đã khám sức khỏe",
        color: "#52c41a"
      },
      [DONATION_STATUS.NOT_ELIGIBLE_HEALTH]: {
        text: "Không đủ điều kiện (sau khám)",
        color: "#ff4d4f"
      },
      [DONATION_STATUS.DONATED]: {
        text: "Đã hiến máu",
        color: "#722ed1"
      },
      [DONATION_STATUS.BLOOD_TESTED]: {
        text: "Đã xét nghiệm",
        color: "#13c2c2"
      },
      [DONATION_STATUS.NOT_ELIGIBLE_TEST]: {
        text: "Không đủ điều kiện (sau xét nghiệm)",
        color: "#ff4d4f"
      },
      [DONATION_STATUS.COMPLETED]: {
        text: "Hoàn thành",
        color: "#52c41a"
      },
      [DONATION_STATUS.STORED]: {
        text: "Đã nhập kho",
        color: "#389e0d"
      }
    };

    return statusMap[numericStatus] || statusMap[status] || { text: status?.toString() || "N/A", color: "#666" };
  },

  getDonationStatusTransitions: (currentStatus, userRole, doctorType) => {
    // Handle numeric status transitions
    const numericStatus = typeof currentStatus === 'string' ? parseInt(currentStatus) : currentStatus;

    // Define allowed transitions for numeric statuses
    const numericTransitions = {
      0: [1, 2, 3], // Pending -> Rejected, Approved, Cancelled
      1: [0],       // Rejected -> Pending (re-review)
      2: [3],       // Approved -> Cancelled
      3: [0]        // Cancelled -> Pending (re-activate)
    };

    // Check if it's a numeric status
    if (typeof numericStatus === 'number' && numericTransitions[numericStatus]) {
      return numericTransitions[numericStatus];
    }

    // Fallback to legacy string-based transitions
    const transitions = STATUS_TRANSITIONS.DONATION[currentStatus] || [];
    const permissions = WORKFLOW_PERMISSIONS.DONATION[userRole];

    if (!permissions) return [];

    return transitions.filter(status =>
      permissions.allowedStatuses.includes(status)
    );
  }
};

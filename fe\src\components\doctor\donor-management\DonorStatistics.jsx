import React from "react";
import { <PERSON>, Row, Col, Statistic } from "antd";

/**
 * Component hiển thị thống kê người hiến máu
 */
const DonorStatistics = ({ statistics }) => {
  const {
    todayCount,
    pendingCount,
    approvedCount,
    rejectedCount,
    cancelledCount,
    totalCount
  } = statistics;

  return (
    <>
      {/* Thống kê chính */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic title="Hôm nay" value={todayCount} prefix="📅" />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic title="Chờ duyệt" value={pendingCount} prefix="⏳" />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Chấp nhận"
              value={approvedCount}
              prefix="✅"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic title="Tổng cộng" value={totalCount} prefix="👥" />
          </Card>
        </Col>
      </Row>

      {/* Thống kê bổ sung */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic title="Không chấp nhận" value={rejectedCount} prefix="❌" />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic title="Đã hủy" value={cancelledCount} prefix="🚫" />
          </Card>
        </Col>
      </Row>
    </>
  );
};

export default DonorStatistics;

import { DONATION_STATUSES } from "../components/shared/ProcessWorkflowModal";

/**
 * Business logic helpers for donation schedule management
 */

// Filter donations for schedule tab (only registered)
export const getScheduleDonations = (allDonations, filters, scheduleSort) => {
  let filtered = allDonations.filter(
    (d) => d.status === DONATION_STATUSES.REGISTERED
  );

  if (filters.bloodType !== "all") {
    filtered = filtered.filter((d) => d.bloodType === filters.bloodType);
  }

  // Sort donations
  return filtered.sort((a, b) => {
    const { field, order } = scheduleSort;
    let aValue, bValue;

    switch (field) {
      case "registrationDate":
        aValue = new Date(a.registrationDate);
        bValue = new Date(b.registrationDate);
        break;
      case "bloodType":
        aValue = a.bloodType;
        bValue = b.bloodType;
        break;
      case "expectedQuantity":
        aValue = parseInt(a.expectedQuantity);
        bValue = parseInt(b.expectedQuantity);
        break;
      default:
        aValue = a[field];
        bValue = b[field];
    }

    if (order === "asc") {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });
};

// Filter donations for process tab (exclude registered)
export const getProcessDonations = (allDonations, filters, processSort) => {
  let filtered = allDonations.filter(
    (d) => d.status !== DONATION_STATUSES.REGISTERED
  );

  if (filters.bloodType !== "all") {
    filtered = filtered.filter((d) => d.bloodType === filters.bloodType);
  }

  if (filters.status !== "all") {
    filtered = filtered.filter((d) => d.status === filters.status);
  }

  // Sort donations
  return filtered.sort((a, b) => {
    const { field, order } = processSort;
    let aValue, bValue;

    switch (field) {
      case "status": {
        // Sort by process order
        const statusOrder = {
          [DONATION_STATUSES.HEALTH_CHECKED]: 1,
          [DONATION_STATUSES.BLOOD_TAKEN]: 2,
          [DONATION_STATUSES.BLOOD_TESTED]: 3,
          [DONATION_STATUSES.STORED]: 4,
        };
        aValue = statusOrder[a.status] || 0;
        bValue = statusOrder[b.status] || 0;
        break;
      }
      case "registrationDate":
        aValue = new Date(a.registrationDate);
        bValue = new Date(b.registrationDate);
        break;
      case "appointmentDate":
        aValue = new Date(a.appointmentDate);
        bValue = new Date(b.appointmentDate);
        break;
      case "bloodType":
        aValue = a.bloodType;
        bValue = b.bloodType;
        break;
      default:
        aValue = a[field];
        bValue = b[field];
    }

    if (order === "asc") {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });
};

// Get status info for display
export const getStatusInfo = (status) => {
  const statusMap = {
    [DONATION_STATUSES.REGISTERED]: {
      text: "Đã đăng ký",
      color: "#1890ff",
      icon: "UserOutlined",
    },
    [DONATION_STATUSES.HEALTH_CHECKED]: {
      text: "Đã khám sức khỏe cơ bản",
      color: "#52c41a",
      icon: "CheckCircleOutlined",
    },
    [DONATION_STATUSES.BLOOD_TAKEN]: {
      text: "Đã lấy máu",
      color: "#722ed1",
      icon: "HeartOutlined",
    },
    [DONATION_STATUSES.BLOOD_TESTED]: {
      text: "Đã xét nghiệm máu",
      color: "#fa8c16",
      icon: "ClockCircleOutlined",
    },
    [DONATION_STATUSES.STORED]: {
      text: "Đã nhập kho",
      color: "#13c2c2",
      icon: "CheckCircleOutlined",
    },
  };
  return statusMap[status] || statusMap[DONATION_STATUSES.REGISTERED];
};

// Format date for display
export const formatDate = (date) => {
  if (!date) return "Chưa có";
  return new Date(date).toLocaleDateString("vi-VN");
};

// Format time slot for display
export const formatTimeSlot = (timeSlot) => {
  const timeSlotMap = {
    morning: "Sáng (7:00-11:00)",
    afternoon: "Chiều (13:00-17:00)",
  };
  return timeSlotMap[timeSlot] || timeSlot;
};

// Get notification status info
export const getNotificationStatusInfo = (status) => {
  const statusMap = {
    sent: { text: "Đã gửi nhắc nhở", color: "#52c41a" },
    pending: { text: "Chưa gửi", color: "#faad14" },
    failed: { text: "Gửi thất bại", color: "#ff4d4f" },
  };
  return statusMap[status] || statusMap.pending;
};

// Blood type options for filters
export const BLOOD_TYPE_OPTIONS = [
  { value: "all", label: "Tất cả" },
  { value: "A+", label: "A+" },
  { value: "A-", label: "A-" },
  { value: "B+", label: "B+" },
  { value: "B-", label: "B-" },
  { value: "AB+", label: "AB+" },
  { value: "AB-", label: "AB-" },
  { value: "O+", label: "O+" },
  { value: "O-", label: "O-" },
];

// Status options for process filter
export const STATUS_OPTIONS = [
  { value: "all", label: "Tất cả" },
  { value: DONATION_STATUSES.HEALTH_CHECKED, label: "Khám sức khỏe cơ bản" },
  { value: DONATION_STATUSES.BLOOD_TAKEN, label: "Lấy máu" },
  { value: DONATION_STATUSES.BLOOD_TESTED, label: "Xét nghiệm máu" },
  { value: DONATION_STATUSES.STORED, label: "Đã nhập kho" },
];

// Sort options for schedule
export const SCHEDULE_SORT_OPTIONS = [
  { value: "appointmentDate-desc", label: "Ngày hẹn (mới nhất)" },
  { value: "appointmentDate-asc", label: "Ngày hẹn (cũ nhất)" },
  { value: "bloodType-asc", label: "Loại máu (A-Z)" },
  { value: "bloodType-desc", label: "Loại máu (Z-A)" },
  { value: "expectedQuantity-asc", label: "Lượng máu (tăng dần)" },
  { value: "expectedQuantity-desc", label: "Lượng máu (giảm dần)" },
];

// Sort options for process
export const PROCESS_SORT_OPTIONS = [
  { value: "status-asc", label: "Trạng thái (theo quy trình)" },
  { value: "appointmentDate-desc", label: "Ngày hẹn (mới nhất)" },
  { value: "appointmentDate-asc", label: "Ngày hẹn (cũ nhất)" },
  { value: "bloodType-asc", label: "Loại máu (A-Z)" },
  { value: "bloodType-desc", label: "Loại máu (Z-A)" },
];

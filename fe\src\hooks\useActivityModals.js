import { useState, useCallback } from "react";
import bloodDonationService from "../services/bloodDonationService";

/**
 * Custom hook để quản lý modal states
 * Cập nhật để lấy thông tin chi tiết từ API
 */
const useActivityModals = () => {
  const [selectedActivity, setSelectedActivity] = useState(null);
  const [showWorkflowModal, setShowWorkflowModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [loadingDetails, setLoadingDetails] = useState(false);

  // View appointment details - cập nhật để lấy thông tin chi tiết từ API
  const handleViewDetails = useCallback(async (activity) => {
    setLoadingDetails(true);

    try {
      // Nếu là donation appointment, lấy thông tin chi tiết từ API
      if (activity.type === "donation" && activity.id) {
        console.log("🔍 Fetching detailed appointment info for ID:", activity.id);

        try {
          const detailedData = await bloodDonationService.getAppointmentDetails(activity.id);
          console.log("✅ Detailed appointment data:", detailedData);

          // Merge thông tin chi tiết vào activity
          const enhancedActivity = {
            ...activity,
            // Cập nhật thông tin sức khỏe chi tiết
            healthCheck: {
              heartRate: detailedData.HeartRate || detailedData.heartRate || activity.healthCheck?.heartRate || "",
              bloodPressure: detailedData.BloodPressure || detailedData.bloodPressure || activity.healthCheck?.bloodPressure || "",
              hemoglobin: detailedData.Hemoglobin || detailedData.hemoglobin || activity.healthCheck?.hemoglobin || "",
              temperature: detailedData.Temperature || detailedData.temperature || activity.healthCheck?.temperature || "",
              weight: detailedData.Weight || detailedData.weight || activity.weight || 0,
              height: detailedData.Height || detailedData.height || activity.height || 0,
            },
            // Cập nhật ghi chú bác sĩ
            doctorNotes: detailedData.DoctorNotes || detailedData.doctorNotes || activity.doctorNotes || "",
            notes: detailedData.Notes || detailedData.notes || activity.notes || "",
          };

          setSelectedActivity(enhancedActivity);
        } catch (apiError) {
          console.warn("⚠️ Could not fetch detailed appointment info, using existing data:", apiError);
          setSelectedActivity(activity);
        }
      } else {
        // Cho blood requests hoặc khi không có ID, sử dụng dữ liệu hiện có
        setSelectedActivity(activity);
      }

      setShowDetailModal(true);
    } catch (error) {
      console.error("❌ Error in handleViewDetails:", error);
      setSelectedActivity(activity);
      setShowDetailModal(true);
    } finally {
      setLoadingDetails(false);
    }
  }, []);

  // View workflow - giữ nguyên logic gốc
  const handleViewWorkflow = useCallback((activity) => {
    setSelectedActivity(activity);
    setShowWorkflowModal(true);
  }, []);

  // Close modals
  const closeDetailModal = useCallback(() => {
    setShowDetailModal(false);
    setSelectedActivity(null);
  }, []);

  const closeWorkflowModal = useCallback(() => {
    setShowWorkflowModal(false);
    setSelectedActivity(null);
  }, []);

  return {
    // State
    selectedActivity,
    showWorkflowModal,
    showDetailModal,
    loadingDetails,

    // Actions
    handleViewDetails,
    handleViewWorkflow,
    closeDetailModal,
    closeWorkflowModal,
    setSelectedActivity,
    setShowWorkflowModal,
    setShowDetailModal,
  };
};

export default useActivityModals;

import { useState, useCallback } from "react";
import patientService from "../services/patientService";

/**
 * Hook để auto-fill thông tin bệnh nhân khi doctor nhập patientId
 */
const usePatientAutoFill = () => {
  const [loading, setLoading] = useState(false);
  const [patientData, setPatientData] = useState(null);
  const [error, setError] = useState(null);

  // Auto-fill patient information by ID
  const fillPatientInfo = useCallback(async (patientId) => {
    if (!patientId || patientId.trim() === "") {
      setPatientData(null);
      setError(null);
      return null;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await patientService.getPatientById(patientId);

      if (response.success && response.data) {
        const patient = response.data;

        // Calculate age from date of birth if available
        const calculateAge = (dateOfBirth) => {
          if (!dateOfBirth) return "";
          const today = new Date();
          const birthDate = new Date(dateOfBirth);
          let age = today.getFullYear() - birthDate.getFullYear();
          const monthDiff = today.getMonth() - birthDate.getMonth();
          if (
            monthDiff < 0 ||
            (monthDiff === 0 && today.getDate() < birthDate.getDate())
          ) {
            age--;
          }
          return age;
        };

        // Format data for form auto-fill
        const formattedData = {
          patientName: patient.name || patient.patientName || "",
          patientAge: patient.age || calculateAge(patient.dateOfBirth) || "",
          patientGender: patient.gender || "",
          bloodGroup: patient.bloodGroup || "",
          rhType: patient.rhType || "",
          medicalHistory: patient.medicalHistory || "",
          allergies: patient.allergies || "",
          currentMedications: patient.currentMedications || "",
          emergencyContact: patient.emergencyContact || "",
          emergencyPhone: patient.emergencyPhone || "",
        };

        setPatientData(formattedData);
        return formattedData;
      } else {
        setError(response.error || "Không tìm thấy thông tin bệnh nhân");
        setPatientData(null);
        return null;
      }
    } catch (err) {
      setError("Có lỗi xảy ra khi lấy thông tin bệnh nhân");
      setPatientData(null);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // Search patients for dropdown suggestions
  const [searchResults, setSearchResults] = useState([]);
  const [searchLoading, setSearchLoading] = useState(false);

  const searchPatients = useCallback(async (searchTerm) => {
    if (!searchTerm || searchTerm.length < 2) {
      setSearchResults([]);
      return;
    }

    try {
      setSearchLoading(true);
      const response = await patientService.searchPatients(searchTerm);

      if (response.success) {
        setSearchResults(response.data || []);
      } else {
        setSearchResults([]);
      }
    } catch (err) {
      setSearchResults([]);
    } finally {
      setSearchLoading(false);
    }
  }, []);

  // Clear all data
  const clearPatientData = useCallback(() => {
    setPatientData(null);
    setError(null);
    setSearchResults([]);
  }, []);

  return {
    // Patient auto-fill
    loading,
    patientData,
    error,
    fillPatientInfo,
    clearPatientData,

    // Patient search
    searchResults,
    searchLoading,
    searchPatients,
  };
};

export default usePatientAutoFill;

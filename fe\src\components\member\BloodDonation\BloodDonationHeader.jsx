import React from "react";
import { Steps, Typography } from "antd";
import {
  UserOutlined,
  HeartOutlined,
  CalendarOutlined,
} from "@ant-design/icons";

const { Title, Text } = Typography;

/**
 * Component hiển thị header và steps navigation cho blood donation form
 */
const BloodDonationHeader = ({ currentStep }) => {
  return (
    <div className="page-header">
      {/* Hero Section */}
      <div className="hero-section">
        {/* Background decoration */}
        <div className="hero-decoration-1" />
        <div className="hero-decoration-2" />

        <div className="hero-content">
          <Title level={1} className="hero-title ">
            🩸 Đăng ký hiến máu
          </Title>
          <Text className="hero-subtitle">
            Hoàn thành các bước để đăng ký hiến máu
          </Text>
        </div>
      </div>

      {/* Steps Navigation */}
      <div className="steps-navigation">
        <Steps
          current={currentStep - 1}
          className="custom-steps"
          size="default"
          items={[
            {
              title: <span className="step-title">Thông tin cá nhân</span>,
              icon: <UserOutlined className="step-icon" />,
              description: (
                <span className="step-description">
                  Kiểm tra & xác nhận thông tin
                </span>
              ),
            },
            {
              title: <span className="step-title">Khảo sát sức khỏe</span>,
              icon: <HeartOutlined className="step-icon" />,
              description: (
                <span className="step-description">
                  Đánh giá tình trạng sức khỏe
                </span>
              ),
            },
            {
              title: <span className="step-title">Đặt lịch hẹn</span>,
              icon: <CalendarOutlined className="step-icon" />,
              description: (
                <span className="step-description">
                  Chọn thời gian phù hợp
                </span>
              ),
            },
          ]}
        />
      </div>
    </div>
  );
};

export default BloodDonationHeader;

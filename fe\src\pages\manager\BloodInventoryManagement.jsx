import React, { useState, useEffect } from "react";
import {
  Button,
  Space,
  Table,
  Card,
  Row,
  Col,
  Select,
  Tag,
  Modal,
  Input,
  InputNumber,
  Checkbox,
  message,
  Statistic,
  Tooltip,
  Tabs,
  Spin,
} from "antd";
import {
  getBloodComponentName,
  getBloodComponentId,
  mapRhTypeToSymbol,
  getInventoryStatus,
  getInventoryStatusColor,
  getInventoryStatusText,
  getInventoryStatusIcon,
  BLOOD_GROUPS,
  RH_TYPES,
  COMPONENT_TYPES,
} from "../../constants/bloodInventoryConstants";
import {
  DatabaseOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  MinusOutlined,
} from "@ant-design/icons";
import ManagerLayout from "../../components/manager/ManagerLayout";
import PageHeader from "../../components/manager/PageHeader";
import BloodInventoryTable from "../../components/shared/BloodInventoryTable";
import {
  fetchBloodInventory,
  checkInBloodInventory,
  checkOutBloodInventory,
} from "../../services/bloodInventoryService";
import authService from "../../services/authService";
import "../../styles/pages/BloodInventoryManagement.scss";
import "../../styles/components/PageHeader.scss";
import useBloodInventoryHistory from "../../hooks/useBloodInventoryHistory";
import ManagerBloodCheckInModal from "../../components/manager/blood-inventory/ManagerBloodCheckInModal";
import ManagerBloodCheckOutModal from "../../components/manager/blood-inventory/ManagerBloodCheckOutModal";
import ManagerBloodInventoryHistoryTable from "../../components/manager/blood-inventory/ManagerBloodInventoryHistoryTable";
import ManagerBloodInventoryHistoryFilters from "../../components/manager/blood-inventory/ManagerBloodInventoryHistoryFilters";
import ManagerBloodInventoryStats from "../../components/manager/blood-inventory/ManagerBloodInventoryStats";

const { Option } = Select;

const BloodInventoryManagement = () => {
  // Helper function to render status icons
  const renderStatusIcon = (status) => {
    const iconName = getInventoryStatusIcon(status);
    switch (iconName) {
      case "ExclamationCircleOutlined":
        return <ExclamationCircleOutlined />;
      case "WarningOutlined":
        return <WarningOutlined />;
      case "CheckCircleOutlined":
        return <CheckCircleOutlined />;
      default:
        return <ExclamationCircleOutlined />;
    }
  };

  const [inventory, setInventory] = useState([]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showUpdateModal, setShowUpdateModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [newInventory, setNewInventory] = useState({
    bloodGroup: "",
    rhType: "Rh+",
    componentType: "Whole",
    quantity: 0,
    isRare: false,
  });
  const [showCheckInModal, setShowCheckInModal] = useState(false);
  const [showCheckOutModal, setShowCheckOutModal] = useState(false);
  const [checkInForm, setCheckInForm] = useState({
    bloodGroup: "",
    rhType: "",
    componentType: "",
    bagType: "",
    quantity: 0,
    notes: "",
  });
  const [checkOutForm, setCheckOutForm] = useState({
    bloodGroup: "",
    rhType: "",
    componentType: "",
    bagType: "",
    quantity: 0,
    notes: "",
  });
  const [loadingCheckIn, setLoadingCheckIn] = useState(false);
  const [loadingCheckOut, setLoadingCheckOut] = useState(false);
  const [activeTab, setActiveTab] = useState("inventory");

  // Lịch sử hoạt động
  const {
    filters: historyFilters,
    setFilters: setHistoryFilters,
    filteredHistory,
    historyLoading,
    performers,
    fetchHistory,
  } = useBloodInventoryHistory();

  useEffect(() => {
    loadInventory();
  }, []);

  // Map lại trạng thái theo số lượng túi
  const loadInventory = async () => {
    try {
      const data = await fetchBloodInventory();
      const inventoryWithStatus = data.map((item) => {
        const bloodType = `${item.bloodGroup}${mapRhTypeToSymbol(item.rhType)}`;
        const status = getInventoryStatus(item.quantity);
        return {
          ...item,
          bloodType,
          status,
          componentType: getBloodComponentName(item.componentId),
          inventoryId: item.InventoryID,
        };
      });
      setInventory(inventoryWithStatus);
    } catch (err) {
      console.error("Failed to load inventory:", err);
      message.error("Không thể tải dữ liệu kho máu");
      setInventory([]);
    }
  };

  // Calculate statistics
  const totalUnits = inventory.reduce((sum, item) => sum + item.quantity, 0);
  const criticalItems = inventory.filter(
    (item) => item.status === "critical"
  ).length;
  const lowItems = inventory.filter((item) => item.status === "low").length;
  const rareBloodUnits = inventory
    .filter((item) => item.isRare)
    .reduce((sum, item) => sum + item.quantity, 0);

  // Xử lý nhập kho
  const handleCheckIn = async () => {
    // Validation form trước khi gửi
    if (
      !checkInForm.bloodGroup ||
      !checkInForm.rhType ||
      !checkInForm.componentType ||
      !checkInForm.quantity ||
      checkInForm.quantity <= 0
    ) {
      message.error("Vui lòng điền đầy đủ thông tin hợp lệ!");
      return;
    }

    setLoadingCheckIn(true);
    try {
      const userId = authService.getCurrentUser()?.id;

      if (!userId) {
        throw new Error("Không tìm thấy thông tin người dùng");
      }

      // Tạo payload đúng format API
      const payload = {
        bloodGroup: checkInForm.bloodGroup,
        rhType: checkInForm.rhType,
        componentId: getBloodComponentId(checkInForm.componentType), // Convert tên thành phần sang ID
        quantity: parseInt(checkInForm.quantity),
        bagType: checkInForm.bagType || "450ml", // Default bag type nếu không có
        notes: checkInForm.notes || "",
        performedBy: parseInt(userId),
      };

      console.log("Check-in payload:", payload); // Debug log

      await checkInBloodInventory(payload);

      // Cập nhật UI sau khi thành công
      setInventory((prev) =>
        prev.map((item) => {
          if (
            item.bloodGroup === checkInForm.bloodGroup &&
            item.rhType === checkInForm.rhType &&
            item.componentType === checkInForm.componentType
          ) {
            return {
              ...item,
              quantity: item.quantity + parseInt(checkInForm.quantity),
            };
          }
          return item;
        })
      );

      message.success("Nhập kho thành công!");
      setShowCheckInModal(false);
      setCheckInForm({
        bloodGroup: "",
        rhType: "",
        componentType: "",
        bagType: "",
        quantity: 0,
        notes: "",
      });
      fetchHistory();
      loadInventory();
    } catch (error) {
      console.error("Check-in error:", error); // Debug log
      const errorMessage =
        error.response?.data?.message || error.message || "Lỗi không xác định";
      message.error(`Nhập kho thất bại: ${errorMessage}`);
    } finally {
      setLoadingCheckIn(false);
    }
  };

  // Xử lý xuất kho
  const handleCheckOut = async () => {
    // Validation form trước khi gửi
    if (
      !checkOutForm.bloodGroup ||
      !checkOutForm.rhType ||
      !checkOutForm.componentType ||
      !checkOutForm.quantity ||
      checkOutForm.quantity <= 0
    ) {
      message.error("Vui lòng điền đầy đủ thông tin hợp lệ!");
      return;
    }

    setLoadingCheckOut(true);
    try {
      const userId = authService.getCurrentUser()?.id;

      if (!userId) {
        throw new Error("Không tìm thấy thông tin người dùng");
      }

      // Kiểm tra số lượng tồn kho trước khi xuất
      const selected = inventory.find(
        (item) =>
          item.bloodGroup === checkOutForm.bloodGroup &&
          item.rhType === checkOutForm.rhType &&
          item.componentType === checkOutForm.componentType
      );

      if (!selected) {
        Modal.warning({
          title: "⚠️ Cảnh báo: Không tìm thấy kho máu phù hợp",
          content: (
            <div>
              <p>
                <strong>Nhóm máu yêu cầu:</strong> {checkOutForm.bloodGroup}
                {checkOutForm.rhType === "Rh+" ? "+" : "-"}
              </p>
              <p>
                <strong>Thành phần:</strong> {checkOutForm.componentType}
              </p>
              <br />
              <p style={{ color: "#ff4d4f" }}>
                Không tìm thấy loại máu phù hợp trong kho. Vui lòng kiểm tra lại
                thông tin hoặc liên hệ bộ phận quản lý kho máu.
              </p>
            </div>
          ),
          okText: "Đã hiểu",
          width: 500,
        });
        setLoadingCheckOut(false);
        return;
      }

      if (selected.quantity < parseInt(checkOutForm.quantity)) {
        Modal.warning({
          title: "⚠️ Cảnh báo: Số lượng máu không đủ",
          content: (
            <div>
              <p>
                <strong>Yêu cầu xuất:</strong> {checkOutForm.quantity} đơn vị
              </p>
              <p>
                <strong>Số lượng tồn kho:</strong> {selected.quantity} đơn vị
              </p>
              <p>
                <strong>Thiếu:</strong>{" "}
                {parseInt(checkOutForm.quantity) - selected.quantity} đơn vị
              </p>
              <br />
              <p style={{ color: "#ff4d4f" }}>
                Không thể thực hiện xuất kho do số lượng tồn kho không đủ. Vui
                lòng kiểm tra lại hoặc liên hệ bộ phận quản lý kho máu.
              </p>
            </div>
          ),
          okText: "Đã hiểu",
          width: 500,
        });
        setLoadingCheckOut(false);
        return;
      }

      // Tạo payload đúng format API
      const payload = {
        bloodGroup: checkOutForm.bloodGroup,
        rhType: checkOutForm.rhType,
        componentId: getBloodComponentId(checkOutForm.componentType), // Convert tên thành phần sang ID
        quantity: parseInt(checkOutForm.quantity),
        bagType: checkOutForm.bagType || "450ml", // Default bag type nếu không có
        notes: checkOutForm.notes || "",
        performedBy: parseInt(userId),
      };

      console.log("Check-out payload:", payload); // Debug log

      await checkOutBloodInventory(payload);

      // Cập nhật UI sau khi thành công
      setInventory((prev) =>
        prev.map((item) => {
          if (
            item.bloodGroup === checkOutForm.bloodGroup &&
            item.rhType === checkOutForm.rhType &&
            item.componentType === checkOutForm.componentType
          ) {
            return {
              ...item,
              quantity: Math.max(
                0,
                item.quantity - parseInt(checkOutForm.quantity)
              ),
            };
          }
          return item;
        })
      );

      message.success("Xuất kho thành công!");
      setShowCheckOutModal(false);
      setCheckOutForm({
        bloodGroup: "",
        rhType: "",
        componentType: "",
        bagType: "",
        quantity: 0,
        notes: "",
      });
      fetchHistory();
      loadInventory();
    } catch (error) {
      console.error("Check-out error:", error); // Debug log
      const errorMessage =
        error.response?.data?.message || error.message || "Lỗi không xác định";
      message.error(`Xuất kho thất bại: ${errorMessage}`);
    } finally {
      setLoadingCheckOut(false);
    }
  };

  // Các cột action riêng cho Manager
  const getManagerActionColumns = () => [
    {
      title: "Thao tác",
      key: "actions",
      width: 120,
      align: "center",
      render: (_, record) => (
        <Space>
          <Tooltip title="Chỉnh sửa">
            <Button
              type="text"
              icon={<EditOutlined />}
              size="small"
              onClick={() => {
                setSelectedItem(record);
                setShowUpdateModal(true);
              }}
              style={{ color: "#1890ff" }}
            />
          </Tooltip>
          <Tooltip title="Xóa">
            <Button
              type="text"
              icon={<DeleteOutlined />}
              size="small"
              onClick={() => handleDeleteItem(record.inventoryId)}
              style={{ color: "#ff4d4f" }}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const handleUpdateQuantity = (inventoryID, newQuantity) => {
    setInventory((prev) =>
      prev.map((item) => {
        if (item.inventoryId === inventoryID) {
          const updatedItem = { ...item, quantity: newQuantity };

          // Recalculate status
          let status = "normal";
          if (newQuantity <= 2) {
            status = "critical";
          } else if (newQuantity <= 5) {
            status = "low";
          } else if (newQuantity >= 30) {
            status = "high";
          }

          updatedItem.status = status;
          updatedItem.statusIcon = getInventoryStatusIcon(status);
          updatedItem.lastUpdated = new Date().toISOString();

          return updatedItem;
        }
        return item;
      })
    );
  };

  const handleAddInventory = () => {
    const newItem = {
      inventoryId: inventory.length + 1,
      bloodGroup: newInventory.bloodGroup,
      rhType: newInventory.rhType,
      bloodType: `${newInventory.bloodGroup}${newInventory.rhType}`,
      componentType: newInventory.componentType,
      quantity: newInventory.quantity,
      isRare: newInventory.isRare,
      lastUpdated: new Date().toISOString(),
    };

    // Calculate status
    let status = "normal";
    if (newItem.quantity <= 2) {
      status = "critical";
    } else if (newItem.quantity <= 5) {
      status = "low";
    } else if (newItem.quantity >= 30) {
      status = "high";
    }

    newItem.status = status;
    newItem.statusIcon = getInventoryStatusIcon(status);

    setInventory((prev) => [...prev, newItem]);
    setShowAddModal(false);
    setNewInventory({
      bloodGroup: "",
      rhType: "",
      componentType: "Whole",
      quantity: 0,
      isRare: false,
    });
    message.success("Đã thêm kho máu mới thành công!");
  };

  const handleUpdateItem = () => {
    if (selectedItem) {
      handleUpdateQuantity(selectedItem.inventoryId, selectedItem.quantity);
      setShowUpdateModal(false);
      setSelectedItem(null);
      message.success("Đã cập nhật kho máu thành công!");
    }
  };

  const handleDeleteItem = (inventoryId) => {
    Modal.confirm({
      title: "Xác nhận xóa",
      content: "Bạn có chắc chắn muốn xóa mục này khỏi kho máu?",
      okText: "Xóa",
      cancelText: "Hủy",
      okType: "danger",
      onOk: () => {
        setInventory((prev) =>
          prev.filter((item) => item.inventoryId !== inventoryId)
        );
        message.success("Đã xóa mục khỏi kho máu thành công!");
      },
    });
  };

  return (
    <ManagerLayout pageTitle="Quản lý kho máu">
      <div className="blood-inventory-management-page">
        <PageHeader title="Quản lý kho máu" icon={DatabaseOutlined} />

        {/* Tabs + 2 nút nhập/xuất kho */}
        <div
          style={{
            display: "flex",
            alignItems: "center",
            marginBottom: 16,
            gap: 12,
          }}
        >
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            items={[
              { key: "inventory", label: "Kho máu" },
              { key: "history", label: "Lịch sử hoạt động" },
            ]}
            style={{ flex: 1 }}
          />
          {activeTab === "inventory" && (
            <>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                style={{ backgroundColor: "#52c41a", borderColor: "#52c41a" }}
                onClick={() => setShowCheckInModal(true)}
              >
                Nhập kho
              </Button>
              <Button
                type="primary"
                icon={<MinusOutlined />}
                style={{ backgroundColor: "#D91022", borderColor: "#D91022" }}
                onClick={() => setShowCheckOutModal(true)}
              >
                Xuất kho
              </Button>
            </>
          )}
        </div>

        {/* Tab content */}
        {activeTab === "inventory" && (
          <>
            <ManagerBloodInventoryStats
              totalUnits={totalUnits}
              criticalItems={criticalItems}
              lowItems={lowItems}
              rareBloodUnits={rareBloodUnits}
            />
            <BloodInventoryTable
              data={inventory}
              showActions={true} // Manager có quyền thao tác
              actionColumns={getManagerActionColumns()}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `${range[0]}-${range[1]} của ${total} mục`,
              }}
              scroll={{ x: 800 }}
            />
          </>
        )}
        {activeTab === "history" && (
          <Card>
            <ManagerBloodInventoryHistoryFilters
              filters={historyFilters}
              setFilters={setHistoryFilters}
              inventory={inventory}
              performers={performers}
            />
            <ManagerBloodInventoryHistoryTable
              data={filteredHistory}
              loading={historyLoading}
            />
          </Card>
        )}
      </div>

      {/* Add Inventory Modal */}
      <Modal
        title="Thêm kho máu mới"
        open={showAddModal}
        onOk={handleAddInventory}
        onCancel={() => setShowAddModal(false)}
        okText="Thêm"
        cancelText="Hủy"
        okButtonProps={{
          disabled: !newInventory.bloodGroup || !newInventory.rhType,
          style: { backgroundColor: "#D93E4C", borderColor: "#D93E4C" },
        }}
        width={600}
      >
        <div style={{ padding: "16px 0" }}>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <div style={{ marginBottom: 8 }}>
                <label style={{ fontWeight: "bold", color: "#20374E" }}>
                  Nhóm máu:
                </label>
              </div>
              <Select
                value={newInventory.bloodGroup}
                onChange={(value) =>
                  setNewInventory((prev) => ({
                    ...prev,
                    bloodGroup: value,
                  }))
                }
                style={{ width: "100%" }}
                placeholder="Chọn nhóm máu"
              >
                {BLOOD_GROUPS.map((group) => (
                  <Option key={group} value={group}>
                    {group}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col span={12}>
              <div style={{ marginBottom: 8 }}>
                <label style={{ fontWeight: "bold", color: "#20374E" }}>
                  Rh:
                </label>
              </div>
              <Select
                value={newInventory.rhType}
                onChange={(value) =>
                  setNewInventory((prev) => ({
                    ...prev,
                    rhType: value,
                  }))
                }
                style={{ width: "100%" }}
                placeholder="Chọn Rh"
              >
                {RH_TYPES.map((rh) => (
                  <Option key={rh} value={rh}>
                    {rh}
                  </Option>
                ))}
              </Select>
            </Col>
          </Row>

          <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
            <Col span={12}>
              <div style={{ marginBottom: 8 }}>
                <label style={{ fontWeight: "bold", color: "#20374E" }}>
                  Thành phần:
                </label>
              </div>
              <Select
                value={newInventory.componentType}
                onChange={(value) =>
                  setNewInventory((prev) => ({
                    ...prev,
                    componentType: value,
                  }))
                }
                style={{ width: "100%" }}
                placeholder="Chọn thành phần"
              >
                {Object.values(COMPONENT_TYPES).map((component) => (
                  <Option key={component} value={component}>
                    {component}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col span={12}>
              <div style={{ marginBottom: 8 }}>
                <label style={{ fontWeight: "bold", color: "#20374E" }}>
                  Số lượng:
                </label>
              </div>
              <InputNumber
                value={newInventory.quantity}
                onChange={(value) =>
                  setNewInventory((prev) => ({
                    ...prev,
                    quantity: value || 0,
                  }))
                }
                min={0}
                style={{ width: "100%" }}
                placeholder="Nhập số lượng"
              />
            </Col>
          </Row>

          <Row style={{ marginTop: 16 }}>
            <Col span={24}>
              <Checkbox
                checked={newInventory.isRare}
                onChange={(e) =>
                  setNewInventory((prev) => ({
                    ...prev,
                    isRare: e.target.checked,
                  }))
                }
                style={{ fontWeight: "bold", color: "#20374E" }}
              >
                Máu hiếm
              </Checkbox>
            </Col>
          </Row>
        </div>
      </Modal>

      {/* Update Inventory Modal */}
      <Modal
        title={`Cập nhật kho máu ${selectedItem?.bloodType || ""}`}
        open={showUpdateModal}
        onOk={handleUpdateItem}
        onCancel={() => setShowUpdateModal(false)}
        okText="Cập nhật"
        cancelText="Hủy"
        okButtonProps={{
          style: { backgroundColor: "#D93E4C", borderColor: "#D93E4C" },
        }}
        width={500}
      >
        {selectedItem && (
          <div style={{ padding: "16px 0" }}>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <div style={{ marginBottom: 8 }}>
                  <label style={{ fontWeight: "bold", color: "#20374E" }}>
                    Nhóm máu:
                  </label>
                </div>
                <Input value={selectedItem.bloodType} disabled />
              </Col>
              <Col span={12}>
                <div style={{ marginBottom: 8 }}>
                  <label style={{ fontWeight: "bold", color: "#20374E" }}>
                    Thành phần:
                  </label>
                </div>
                <Input value={selectedItem.componentType} disabled />
              </Col>
            </Row>

            <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
              <Col span={12}>
                <div style={{ marginBottom: 8 }}>
                  <label style={{ fontWeight: "bold", color: "#20374E" }}>
                    Số lượng hiện tại:
                  </label>
                </div>
                <InputNumber
                  value={selectedItem.quantity}
                  onChange={(value) =>
                    setSelectedItem((prev) => ({
                      ...prev,
                      quantity: value || 0,
                    }))
                  }
                  min={0}
                  style={{ width: "100%" }}
                />
              </Col>
              <Col span={12}>
                <div style={{ marginBottom: 8 }}>
                  <label style={{ fontWeight: "bold", color: "#20374E" }}>
                    Trạng thái hiện tại:
                  </label>
                </div>
                <Tag
                  icon={renderStatusIcon(selectedItem.status)}
                  color={getInventoryStatusColor(selectedItem.status)}
                  style={{ fontWeight: "bold", padding: "4px 12px" }}
                >
                  {getInventoryStatusText(selectedItem.status)}
                </Tag>
              </Col>
            </Row>
          </div>
        )}
      </Modal>

      {/* Modal Nhập kho */}
      <ManagerBloodCheckInModal
        open={showCheckInModal}
        onOk={handleCheckIn}
        onCancel={() => setShowCheckInModal(false)}
        confirmLoading={loadingCheckIn}
        inventory={inventory}
        form={checkInForm}
        setForm={setCheckInForm}
      />

      {/* Modal Xuất kho */}
      <ManagerBloodCheckOutModal
        open={showCheckOutModal}
        onOk={handleCheckOut}
        onCancel={() => setShowCheckOutModal(false)}
        confirmLoading={loadingCheckOut}
        inventory={inventory}
        form={checkOutForm}
        setForm={setCheckOutForm}
      />
    </ManagerLayout>
  );
};

export default BloodInventoryManagement;

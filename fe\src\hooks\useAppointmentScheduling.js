import dayjs from "dayjs";
import bloodDonationService from "../services/bloodDonationService";
import RemindService from "../services/remindService";

/**
 * Custom hook để quản lý logic đặt lịch hẹn hiến máu
 */
export const useAppointmentScheduling = (
  currentUser,
  healthSurvey,
  appointmentData,
  setLoading,
  setRegistrationResult
) => {
  const getTimeSlotText = (slot) => {
    return slot === "morning" ? "7:00 - 12:00 (Sáng)" : "13:00 - 17:00 (<PERSON><PERSON><PERSON>)";
  };

  const validateAppointmentData = () => {
    if (!appointmentData.preferredDate) {
      alert("Vui lòng chọn ngày đặt lịch!");
      return false;
    }
    if (!appointmentData.timeSlot) {
      alert("Vui lòng chọn khung giờ đặt lịch!");
      return false;
    }
    if (!healthSurvey.weight) {
      alert("<PERSON>ui lòng nhập cân nặng!");
      return false;
    }

    // Validate 84-day gap if user has donated before
    if (healthSurvey.hasDonatedBefore && healthSurvey.lastDonationDate) {
      const lastDonationDate = dayjs(healthSurvey.lastDonationDate);
      const appointmentDate = dayjs(appointmentData.preferredDate);
      const daysDifference = appointmentDate.diff(lastDonationDate, "day");

      if (daysDifference < 84) {
        const earliestDate = lastDonationDate.add(84, "day");
        alert(
          `Bạn cần chờ ít nhất 84 ngày từ lần hiến máu gần nhất (${lastDonationDate.format(
            "DD/MM/YYYY"
          )}). Ngày sớm nhất có thể hiến máu là: ${earliestDate.format(
            "DD/MM/YYYY"
          )}`
        );
        return false;
      }
    }

    return true;
  };

  const handleAppointmentSubmit = async () => {
    setLoading(true);

    // Validate required fields for appointment
    if (!validateAppointmentData()) {
      setLoading(false);
      return;
    }

    try {
      // Step 1: Check last donation date from backend
      console.log("Step 1: Checking last donation date...");
      let lastDonationFromBackend = null;
      try {
        lastDonationFromBackend = await bloodDonationService.getLastDonation(
          currentUser.id
        );
        console.log("Last donation from backend:", lastDonationFromBackend);
      } catch (error) {
        console.log(
          "No previous donation found or error fetching:",
          error.message
        );
      }

      // Step 2: Update self-reported donation date (required by backend)
      console.log("Step 2: Updating self-reported donation date...");

      let selfReportedDate = null;
      if (healthSurvey.hasDonatedBefore === true) {
        // User said they donated before, so we need to provide the date
        if (!healthSurvey.lastDonationDate) {
          setLoading(false);
          alert("Vui lòng nhập ngày hiến máu gần nhất!");
          return;
        }
        selfReportedDate = dayjs(healthSurvey.lastDonationDate).format(
          "YYYY-MM-DD"
        );
      }

      console.log("User has donated before:", healthSurvey.hasDonatedBefore);
      console.log("Self-reported date to submit:", selfReportedDate);

      try {
        await bloodDonationService.updateSelfReportedDonation(
          currentUser.id,
          selfReportedDate
        );
        console.log("Self-reported donation date updated successfully");
      } catch (error) {
        console.error("Error updating self-reported donation:", error);
        setLoading(false);
        alert("Không thể cập nhật thông tin hiến máu. Vui lòng thử lại!");
        return;
      }

      // Step 3: Prepare appointment data
      console.log("Step 3: Creating appointment...");
      const appointmentDate = dayjs(appointmentData.preferredDate)
        .hour(appointmentData.timeSlot === "morning" ? 9 : 15)
        .minute(0)
        .second(0)
        .millisecond(0);

      const apiPayload = {
        UserID: parseInt(currentUser.id),
        AppointmentDate: appointmentDate.format("YYYY-MM-DDTHH:mm:ss"),
        TimeSlot:
          appointmentData.timeSlot === "morning"
            ? "Sáng (7:00-12:00)"
            : "Chiều (13:00-17:00)",
        weight: parseFloat(healthSurvey.weight),
        height: healthSurvey.height ? parseFloat(healthSurvey.height) : null,
        hasDonated: healthSurvey.hasDonatedBefore === true,
        lastDonationDate:
          healthSurvey.hasDonatedBefore && healthSurvey.lastDonationDate
            ? dayjs(healthSurvey.lastDonationDate).format("YYYY-MM-DD") +
            "T00:00:00"
            : null,
      };

      console.log("Sending blood donation appointment request:", apiPayload);

      // Step 4: Create the appointment
      const response = await bloodDonationService.createAppointment(apiPayload);

      console.log("Blood donation appointment response:", response);

      // Create appointment reminder using RemindService
      try {
        const appointmentReminderData = {
          userId: currentUser.id,
          appointmentId: response.data?.appointmentId || Date.now(),
          appointmentDate: `${appointmentData.preferredDate}T${appointmentData.timeSlot === "morning" ? "09:00:00" : "15:00:00"
            }`,
          bloodType: healthSurvey.bloodType || "O+",
          donorName: currentUser.name || currentUser.fullName,
          donorEmail: currentUser.email,
          donorPhone: currentUser.phone
        };

        await RemindService.createAppointmentReminder(appointmentReminderData);
        console.log("Appointment reminder created successfully");
      } catch (reminderError) {
        console.error("Error creating appointment reminder:", reminderError);
        // Don't fail the appointment creation if reminder fails
      }

      setRegistrationResult({
        status: "scheduled",
        message: "ĐẶT LỊCH THÀNH CÔNG",
        description:
          "Lịch hẹn hiến máu đã được gửi đến Manager. Bạn sẽ nhận được xác nhận sớm.",
      });
    } catch (error) {
      console.error("Error scheduling appointment:", error);
      console.error("Error response:", error.response?.data);
      console.error("Error status:", error.response?.status);

      // Handle different types of API errors
      let errorMessage = "Có lỗi xảy ra khi đặt lịch. Vui lòng thử lại.";

      if (error.response) {
        // Server responded with error status
        const status = error.response.status;
        const data = error.response.data;

        console.log("Server error details:", data);

        if (status === 400) {
          // Try to extract detailed error message from server response
          if (typeof data === "string") {
            errorMessage = data;
          } else if (data?.message) {
            errorMessage = data.message;
          } else if (data?.errors) {
            // Handle validation errors
            const validationErrors = Object.values(data.errors).flat();
            errorMessage = validationErrors.join(", ");
          } else if (data?.title) {
            errorMessage = data.title;
          } else {
            errorMessage =
              "Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.";
          }
        } else if (status === 401) {
          errorMessage = "Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.";
        } else if (status === 409) {
          errorMessage =
            data?.message || "Lịch hẹn bị trùng. Vui lòng chọn thời gian khác.";
        } else if (status >= 500) {
          errorMessage = "Lỗi hệ thống. Vui lòng thử lại sau.";
        } else {
          errorMessage = data?.message || errorMessage;
        }
      } else if (error.request) {
        // Network error
        errorMessage =
          "Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng.";
      }

      setRegistrationResult({
        status: "error",
        message: "LỖI ĐẶT LỊCH",
        description: errorMessage,
      });
    } finally {
      setLoading(false);
    }
  };

  return {
    getTimeSlotText,
    validateAppointmentData,
    handleAppointmentSubmit,
  };
};

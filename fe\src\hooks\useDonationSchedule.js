import { useState, useEffect, useCallback } from "react";
import { message } from "antd";
import authService from "../services/authService";
import bloodDonationService from "../services/bloodDonationService";
import userInfoService from "../services/userInfoService";
import { DONATION_STATUSES } from "../components/shared/ProcessWorkflowModal";
import { formatBloodType } from "../utils/bloodDonationUtils";
import useRemindManagement from "./useRemindManagement";

/**
 * Custom hook for managing donation schedule data and operations
 */
const useDonationSchedule = () => {
  const [allDonations, setAllDonations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedDonation, setSelectedDonation] = useState(null);
  const [processModalVisible, setProcessModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState("1");
  const [scheduleSort, setScheduleSort] = useState({
    field: "appointmentDate",
    order: "desc",
  });
  const [processSort, setProcessSort] = useState({
    field: "status",
    order: "asc",
  });
  const [filters, setFilters] = useState({
    bloodType: "all",
    status: "all",
  });

  const currentUser = authService.getCurrentUser();
  const isManager = currentUser?.role === "manager";

  // Use remind management hook
  const { createManualReminder, createAppointmentReminder } = useRemindManagement();

  // Helper function to map API status to component status
  const mapApiStatusToComponentStatus = (apiStatus) => {
    const statusMap = {
      // Numeric statuses
      0: DONATION_STATUSES.REGISTERED,
      1: DONATION_STATUSES.HEALTH_CHECKED,
      2: DONATION_STATUSES.BLOOD_TAKEN,
      3: DONATION_STATUSES.BLOOD_TESTED,
      4: DONATION_STATUSES.STORED,
      // String statuses
      "registered": DONATION_STATUSES.REGISTERED,
      "pending": DONATION_STATUSES.REGISTERED,
      "health_checked": DONATION_STATUSES.HEALTH_CHECKED,
      "blood_taken": DONATION_STATUSES.BLOOD_TAKEN,
      "donated": DONATION_STATUSES.BLOOD_TAKEN,
      "blood_tested": DONATION_STATUSES.BLOOD_TESTED,
      "stored": DONATION_STATUSES.STORED,
    };
    return statusMap[apiStatus] || DONATION_STATUSES.REGISTERED;
  };

  // Load all donations with detailed donor information
  const loadAllDonations = async () => {
    setLoading(true);
    try {
      const donationsData = await bloodDonationService.getAllBloodDonationSubmissions();

      // Transform API data and fetch detailed user information
      const transformedDonations = await Promise.all(
        donationsData.map(async (donation) => {
          let donorInfo = {};

          // Fetch detailed donor information from Information API
          try {
            const userId = donation.userId || donation.donorId || donation.UserID;
            if (userId) {
              donorInfo = await userInfoService.getUserInfo(userId);
            }
          } catch (error) {
            console.warn(`⚠️ Could not fetch donor info for user ${donation.userId}:`, error);
            donorInfo = {};
          }

          // Map API fields to component expected fields with detailed donor info
          return {
            id: donation.appointmentId || donation.id,
            donorId: donation.userId || donation.donorId || donation.UserID,
            donorName: donorInfo.fullName || donorInfo.name || donation.donorName || donation.name || `User ${donation.userId}`,
            donorPhone: donorInfo.phoneNumber || donorInfo.phone || donation.donorPhone || donation.phone || "",
            donorEmail: donorInfo.email || donation.donorEmail || donation.email || "",
            bloodType: donorInfo.bloodGroup && donorInfo.rhType
              ? formatBloodType(donorInfo.bloodGroup, donorInfo.rhType)
              : donation.bloodType || "O+",
            expectedQuantity: donation.expectedQuantity || "450ml",
            registrationDate: donation.registrationDate || donation.createdAt,
            appointmentDate: donation.appointmentDate || donation.date,
            timeSlot: donation.timeSlot || "morning",
            status: mapApiStatusToComponentStatus(donation.status),
            notificationStatus: donation.notificationStatus || "pending",
            notificationSentAt: donation.notificationSentAt || null,
            healthSurvey: {
              weight: donorInfo.weight || donation.weight || 0,
              height: donorInfo.height || donation.height || 0,
              bloodPressure: donation.bloodPressure || "120/80",
              eligibilityChecked: donation.eligibilityChecked || false,
            },
            location: {
              address: donorInfo.address || donation.address || "Chưa có địa chỉ",
              distance: donorInfo.distance || donation.distance || 0,
            },
            notes: donation.notes || "",
            donorDetails: {
              dateOfBirth: donorInfo.dateOfBirth || null,
              gender: donorInfo.gender || null,
              identityCard: donorInfo.identityCard || null,
              occupation: donorInfo.occupation || null,
              emergencyContact: donorInfo.emergencyContact || null,
              medicalHistory: donorInfo.medicalHistory || null,
            }
          };
        })
      );

      setAllDonations(transformedDonations);

    } catch (error) {
      console.error("Error loading donations:", error);

      if (error.response?.status === 404) {
        message.warning("API endpoint chưa sẵn sàng. Đang sử dụng dữ liệu mẫu.");
      } else {
        message.error("Có lỗi xảy ra khi tải danh sách hiến máu!");
      }

      // Fallback to mock data
      setAllDonations([]);
    } finally {
      setLoading(false);
    }
  };

  // Refresh data function
  const refreshData = () => {
    loadAllDonations();
  };

  // Handle store blood action
  const handleStoreBlood = async (donationId) => {
    try {
      await bloodDonationService.updateBloodDonationSubmissionStatus(
        donationId,
        DONATION_STATUSES.STORED
      );

      setAllDonations((prev) =>
        prev.map((d) =>
          d.id === donationId ? { ...d, status: DONATION_STATUSES.STORED } : d
        )
      );
      message.success("Đã nhập kho thành công!");
    } catch (error) {
      console.error("Error storing blood:", error);
      message.error("Có lỗi xảy ra khi nhập kho!");
    }
  };

  // Manual send reminder using RemindService
  const handleSendReminder = async (donation) => {
    try {
      // Create manual reminder using hook
      await createManualReminder(donation);

      // Update donation status
      setAllDonations((prev) =>
        prev.map((d) =>
          d.id === donation.id
            ? {
              ...d,
              notificationStatus: "sent",
              notificationSentAt: new Date().toISOString(),
            }
            : d
        )
      );
    } catch (error) {
      console.error("Error sending manual reminder:", error);

      // Update status to failed
      setAllDonations((prev) =>
        prev.map((d) =>
          d.id === donation.id
            ? { ...d, notificationStatus: "failed" }
            : d
        )
      );
    }
  };

  // Auto notification system using RemindService
  const checkAndSendReminders = useCallback(async () => {
    try {
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(0, 0, 0, 0);

      const tomorrowEnd = new Date(tomorrow);
      tomorrowEnd.setHours(23, 59, 59, 999);

      const donationsToRemind = allDonations.filter((donation) => {
        if (donation.status !== DONATION_STATUSES.REGISTERED) return false;
        if (donation.notificationStatus === "sent") return false;

        const appointmentDate = new Date(donation.appointmentDate);
        return appointmentDate >= tomorrow && appointmentDate <= tomorrowEnd;
      });

      for (const donation of donationsToRemind) {
        try {
          // Create appointment reminder using hook
          const appointmentData = {
            userId: donation.donorId,
            appointmentId: donation.id,
            appointmentDate: donation.appointmentDate,
            bloodType: donation.bloodType,
            donorName: donation.donorName,
            donorEmail: donation.donorEmail,
            donorPhone: donation.donorPhone
          };

          await createAppointmentReminder(appointmentData);

          // Update donation status
          setAllDonations((prev) =>
            prev.map((d) =>
              d.id === donation.id
                ? {
                  ...d,
                  notificationStatus: "sent",
                  notificationSentAt: new Date().toISOString(),
                }
                : d
            )
          );
        } catch (error) {
          console.error(`Error creating reminder for ${donation.donorName}:`, error);
          setAllDonations((prev) =>
            prev.map((d) =>
              d.id === donation.id ? { ...d, notificationStatus: "failed" } : d
            )
          );
        }
      }

      // Auto reminder completed
    } catch (error) {
      console.error("Error in auto reminder system:", error);
    }
  }, [allDonations]);

  // Setup reminder system
  useEffect(() => {
    const reminderInterval = setInterval(
      checkAndSendReminders,
      24 * 60 * 60 * 1000
    );

    const initialCheck = setTimeout(checkAndSendReminders, 5000);

    return () => {
      clearInterval(reminderInterval);
      clearTimeout(initialCheck);
    };
  }, [checkAndSendReminders]);

  useEffect(() => {
    loadAllDonations();
  }, []);

  return {
    // Data
    allDonations,
    loading,
    selectedDonation,
    processModalVisible,
    detailModalVisible,
    activeTab,
    scheduleSort,
    processSort,
    filters,
    isManager,

    // Actions
    setSelectedDonation,
    setProcessModalVisible,
    setDetailModalVisible,
    setActiveTab,
    setScheduleSort,
    setProcessSort,
    setFilters,
    refreshData,
    handleStoreBlood,
    handleSendReminder,
  };
};

export default useDonationSchedule;

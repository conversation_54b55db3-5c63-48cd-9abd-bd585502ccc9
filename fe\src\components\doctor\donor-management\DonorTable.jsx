import React from "react";
import { Table, Tag, Button } from "antd";
import { StatusWorkflowService } from "../../../utils/statusWorkflowService";

/**
 * Component bảng danh sách người hiến máu
 */
const DonorTable = ({
  donors,
  loading,
  onUpdateDonor,
  onUpdateStatus,
  onDeleteAppointment
}) => {

  const getTimeSlotText = (slot) => {
    if (slot === "morning" || slot === "Sáng (7:00-12:00)") {
      return "7:00 - 12:00";
    } else if (slot === "afternoon" || slot === "Chiều (13:00-17:00)") {
      return "13:00 - 17:00";
    }
    return slot || "N/A";
  };

  const BLOOD_TYPES = [
    "O+", "O-", "A+", "A-", "B+", "B-", "AB+", "AB-"
  ];

  const GENDER_FILTERS = [
    { text: "Nam", value: "male" },
    { text: "N<PERSON>", value: "female" },
    { text: "Khác", value: "unknown" },
  ];

  const STATUS_FILTERS = [
    { text: "Đang chờ duyệt", value: 0 },
    { text: "Không chấp nhận", value: 1 },
    { text: "Chấp nhận", value: 2 },
    { text: "Hủy", value: 3 },
  ];

  const TIMESLOT_FILTERS = [
    { text: "Sáng (7:00 - 12:00)", value: "morning" },
    { text: "Chiều (13:00 - 17:00)", value: "afternoon" },
  ];

  const columns = [
    {
      title: "Tên",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "Nhóm máu",
      dataIndex: "bloodType",
      key: "bloodType",
      render: (b) => <Tag color="red">{b}</Tag>,
      filters: BLOOD_TYPES.map(type => ({ text: type, value: type })),
      onFilter: (value, record) => record.bloodType === value,
    },
    {
      title: "Tuổi",
      dataIndex: "age",
      key: "age",
      sorter: (a, b) => a.age - b.age,
    },
    {
      title: "Giới tính",
      dataIndex: "gender",
      key: "gender",
      render: (g) => (g === "male" ? "Nam" : g === "female" ? "Nữ" : "N/A"),
      filters: GENDER_FILTERS,
      onFilter: (value, record) => record.gender === value,
    },
    {
      title: "Điện thoại",
      dataIndex: "phone",
      key: "phone",
    },
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
    },
    {
      title: "Ngày đăng ký",
      dataIndex: "createdAt",
      key: "createdAt",
      render: (d) => (d ? new Date(d).toLocaleDateString("vi-VN") : "N/A"),
      sorter: (a, b) => new Date(a.createdAt) - new Date(b.createdAt),
    },
    {
      title: "Ngày hẹn",
      dataIndex: "appointmentDate",
      key: "appointmentDate",
      render: (d) => (d ? new Date(d).toLocaleDateString("vi-VN") : "N/A"),
    },
    {
      title: "Trạng thái",
      dataIndex: "status",
      key: "status",
      render: (s) => {
        const statusInfo = StatusWorkflowService.getStatusInfo(s);
        return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
      },
      filters: STATUS_FILTERS,
      onFilter: (value, record) => {
        const status = typeof record.status === 'string' ? parseInt(record.status) : record.status;
        return status === value;
      },
    },
    {
      title: "Giờ hẹn",
      dataIndex: "timeSlot",
      key: "timeSlot",
      render: (t) => <Tag color="blue">{getTimeSlotText(t)}</Tag>,
      filters: TIMESLOT_FILTERS,
      onFilter: (value, record) => record.timeSlot === value,
    },
    {
      title: "Hành động",
      key: "actions",
      render: (_, donor) => (
        <div style={{ display: 'flex', gap: '4px', flexWrap: 'wrap' }}>
          <Button type="link" size="small" onClick={() => onUpdateDonor(donor)}>
            Thông tin
          </Button>
          <Button type="link" size="small" onClick={() => onUpdateStatus(donor)}>
            Trạng thái
          </Button>
          <Button
            type="link"
            size="small"
            danger
            onClick={() => {
              if (window.confirm('Bạn có chắc chắn muốn hủy lịch hẹn này?')) {
                onDeleteAppointment(donor.id);
              }
            }}
          >
            Hủy
          </Button>
        </div>
      ),
    },
  ];

  return (
    <Table
      dataSource={donors}
      columns={columns}
      rowKey="id"
      loading={loading}
      pagination={{ pageSize: 8 }}
    />
  );
};

export default DonorTable;

import { useState, useCallback } from "react";
import { bloodRequestService } from "../services/bloodRequestService";
import authService from "../services/authService";

/**
 * Custom hook for creating blood requests
 * Handles form state, validation, and submission
 */
export const useBloodRequestCreation = (onSuccess) => {
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [newRequest, setNewRequest] = useState({
    patientID: 0,
    patientName: "",
    age: 0,
    gender: "",
    bloodGroup: "",
    rhType: "",
    quantity: 1,
    reason: "",
  });

  const currentUser = authService.getCurrentUser();

  const resetNewRequest = useCallback(() => {
    setNewRequest({
      patientID: 0,
      patientName: "",
      age: 0,
      gender: "",
      bloodGroup: "",
      rhType: "",
      quantity: 1,
      reason: "",
    });
  }, []);

  const validateRequest = useCallback(() => {
    if (!newRequest.patientName?.trim()) {
      alert("Vui lòng nhập tên bệnh nhân");
      return false;
    }
    if (!newRequest.age || newRequest.age <= 0) {
      alert("Vui lòng nhập tuổi hợp lệ (lớn hơn 0)");
      return false;
    }
    if (!newRequest.gender?.trim()) {
      alert("Vui lòng chọn giới tính");
      return false;
    }
    if (!newRequest.bloodGroup?.trim()) {
      alert("Vui lòng chọn nhóm máu");
      return false;
    }
    if (!newRequest.rhType?.trim()) {
      alert("Vui lòng chọn Rh");
      return false;
    }
    if (!newRequest.quantity || newRequest.quantity <= 0) {
      alert("Vui lòng nhập số lượng hợp lệ");
      return false;
    }
    if (!newRequest.reason?.trim()) {
      alert("Vui lòng nhập lý do yêu cầu máu");
      return false;
    }
    return true;
  }, [newRequest]);

  const handleCreateRequest = useCallback(async () => {
    try {
      setLoading(true);

      if (!validateRequest()) {
        return;
      }

      // Prepare API request data according to schema
      const requestData = {
        userID: parseInt(currentUser?.id) || 0,
        patientID: newRequest.patientID ? parseInt(newRequest.patientID) : null,
        patientName: newRequest.patientName.trim(),
        age: parseInt(newRequest.age),
        gender: newRequest.gender.trim(),
        relationship: "Bác sĩ phụ trách",
        facilityName:
          currentUser?.hospitalName || currentUser?.hospital || "Bệnh viện",
        doctorName: currentUser?.name || currentUser?.fullName || "",
        doctorPhone: currentUser?.phone || "**********",
        bloodGroup: newRequest.bloodGroup.trim(),
        rhType: newRequest.rhType.trim(),
        quantity: parseInt(newRequest.quantity),
        reason: newRequest.reason.trim(),
        status: 1, // Auto-approved for non-hematology doctors
        createdTime: new Date().toISOString(),
      };

      const response = await bloodRequestService.createBloodRequest(
        requestData
      );

      if (response.success) {
        if (onSuccess) {
          await onSuccess();
        }
        setShowCreateModal(false);
        resetNewRequest();
        alert("Yêu cầu máu đã được tạo thành công!");
      } else {
        alert("Có lỗi xảy ra khi tạo yêu cầu máu: " + response.error);
      }
    } catch (error) {
      console.error("Error creating blood request:", error);
      alert("Có lỗi xảy ra khi tạo yêu cầu máu");
    } finally {
      setLoading(false);
    }
  }, [newRequest, currentUser, validateRequest, onSuccess, resetNewRequest]);

  const openCreateModal = useCallback(() => {
    setShowCreateModal(true);
  }, []);

  const closeCreateModal = useCallback(() => {
    setShowCreateModal(false);
  }, []);

  return {
    showCreateModal,
    loading,
    newRequest,
    setNewRequest,
    handleCreateRequest,
    resetNewRequest,
    openCreateModal,
    closeCreateModal,
  };
};

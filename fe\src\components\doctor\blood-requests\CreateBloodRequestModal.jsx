import React, { useState } from "react";
import { BLOOD_GROUPS, RH_TYPES } from "../../../services/mockData";
import usePatientAutoFill from "../../../hooks/usePatientAutoFill";

/**
 * Modal component for creating blood requests
 * Only shown for non-hematology doctors
 */
const CreateBloodRequestModal = ({
  showCreateModal,
  loading,
  newRequest,
  setNewRequest,
  handleCreateRequest,
  closeCreateModal,
  isBloodDepartment,
}) => {
  const [patientIdInput, setPatientIdInput] = useState("");
  const {
    loading: patientLoading,
    patientData,
    error: patientError,
    fillPatientInfo,
  } = usePatientAutoFill();

  if (!showCreateModal || isBloodDepartment) {
    return null;
  }

  // Handle patient ID change and auto-fill
  const handlePatientIdChange = async (e) => {
    const patientId = e.target.value;
    setPatientIdInput(patientId);

    // Update the patientID in newRequest
    setNewRequest((prev) => ({
      ...prev,
      patientID: parseInt(patientId) || 0,
    }));

    // Auto-fill if patientId is valid
    if (patientId && patientId.trim() !== "") {
      const patientInfo = await fillPatientInfo(patientId);
      if (patientInfo) {
        // Auto-fill form fields
        setNewRequest((prev) => ({
          ...prev,
          patientName: patientInfo.patientName || prev.patientName,
          age: patientInfo.patientAge || prev.age,
          gender: patientInfo.patientGender || prev.gender,
          bloodGroup: patientInfo.bloodGroup || prev.bloodGroup,
          rhType: patientInfo.rhType || prev.rhType,
          // Add other fields as needed
        }));
      }
    }
  };

  return (
    <div className="modal-overlay" onClick={closeCreateModal}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>Tạo yêu cầu máu mới</h2>
          <button className="close-btn" onClick={closeCreateModal}>
            ×
          </button>
        </div>
        <div className="modal-body">
          {/* Patient Information */}
          <div
            className="form-section"
            style={{
              marginBottom: "24px",
              padding: "16px",
              backgroundColor: "#f8f9fa",
              borderRadius: "8px",
            }}
          >
            <h3
              style={{
                marginBottom: "16px",
                color: "#1890ff",
                fontSize: "16px",
                fontWeight: "600",
              }}
            >
              Thông tin bệnh nhân
            </h3>
            <div className="form-row">
              <div className="form-group">
                <label>Mã bệnh nhân (tùy chọn):</label>
                <input
                  type="number"
                  value={patientIdInput}
                  onChange={handlePatientIdChange}
                  placeholder="Nhập mã bệnh nhân để tự động điền thông tin..."
                  disabled={patientLoading}
                />
                {patientLoading && (
                  <small
                    style={{
                      color: "#1890ff",
                      fontSize: "12px",
                      display: "block",
                      marginTop: "4px",
                    }}
                  >
                    🔄 Đang tải thông tin bệnh nhân...
                  </small>
                )}
                {patientError && (
                  <small
                    style={{
                      color: "#ff4d4f",
                      fontSize: "12px",
                      display: "block",
                      marginTop: "4px",
                    }}
                  >
                    ⚠️ {patientError}
                  </small>
                )}
                {patientData && (
                  <small
                    style={{
                      color: "#52c41a",
                      fontSize: "12px",
                      display: "block",
                      marginTop: "4px",
                    }}
                  >
                    ✅ Đã tự động điền thông tin bệnh nhân
                  </small>
                )}
                <small
                  style={{
                    color: "#666",
                    fontSize: "12px",
                    display: "block",
                    marginTop: "4px",
                  }}
                >
                  Nhập mã bệnh nhân để tự động điền thông tin, hoặc để trống để
                  tạo mới
                </small>
              </div>
              <div className="form-group">
                <label>Tên bệnh nhân:</label>
                <input
                  type="text"
                  value={newRequest.patientName}
                  onChange={(e) =>
                    setNewRequest((prev) => ({
                      ...prev,
                      patientName: e.target.value,
                    }))
                  }
                  placeholder="Nhập tên bệnh nhân..."
                />
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label>Tuổi:</label>
                <input
                  type="number"
                  value={newRequest.age}
                  onChange={(e) =>
                    setNewRequest((prev) => ({
                      ...prev,
                      age: parseInt(e.target.value) || 0,
                    }))
                  }
                  min="0"
                  max="120"
                  placeholder="Nhập tuổi..."
                />
              </div>
              <div className="form-group">
                <label>Giới tính:</label>
                <select
                  value={newRequest.gender}
                  onChange={(e) =>
                    setNewRequest((prev) => ({
                      ...prev,
                      gender: e.target.value,
                    }))
                  }
                >
                  <option value="">Chọn giới tính</option>
                  <option value="Nam">Nam</option>
                  <option value="Nữ">Nữ</option>
                  <option value="Khác">Khác</option>
                </select>
              </div>
            </div>
          </div>

          {/* Blood Information */}
          <div
            className="form-section"
            style={{
              marginBottom: "24px",
              padding: "16px",
              backgroundColor: "#f8f9fa",
              borderRadius: "8px",
            }}
          >
            <h3
              style={{
                marginBottom: "16px",
                color: "#1890ff",
                fontSize: "16px",
                fontWeight: "600",
              }}
            >
              Thông tin máu cần thiết
            </h3>
            <div className="form-row">
              <div className="form-group">
                <label>Nhóm máu:</label>
                <select
                  value={newRequest.bloodGroup}
                  onChange={(e) =>
                    setNewRequest((prev) => ({
                      ...prev,
                      bloodGroup: e.target.value,
                    }))
                  }
                >
                  <option value="">Chọn nhóm máu</option>
                  {Object.values(BLOOD_GROUPS).map((group) => (
                    <option key={group} value={group}>
                      {group}
                    </option>
                  ))}
                </select>
              </div>
              <div className="form-group">
                <label>Rh:</label>
                <select
                  value={newRequest.rhType}
                  onChange={(e) =>
                    setNewRequest((prev) => ({
                      ...prev,
                      rhType: e.target.value,
                    }))
                  }
                >
                  <option value="">Chọn Rh</option>
                  {Object.values(RH_TYPES).map((rh) => (
                    <option key={rh} value={rh}>
                      {rh}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label>Số lượng (ml):</label>
                <input
                  type="number"
                  value={newRequest.quantity}
                  onChange={(e) =>
                    setNewRequest((prev) => ({
                      ...prev,
                      quantity: parseInt(e.target.value) || 1,
                    }))
                  }
                  min="1"
                  placeholder="Nhập số lượng ml..."
                />
              </div>
            </div>
          </div>

          {/* Medical Reason */}
          <div
            className="form-section"
            style={{
              marginBottom: "24px",
              padding: "16px",
              backgroundColor: "#f8f9fa",
              borderRadius: "8px",
            }}
          >
            <h3
              style={{
                marginBottom: "16px",
                color: "#1890ff",
                fontSize: "16px",
                fontWeight: "600",
              }}
            >
              Lý do y tế
            </h3>
            <div className="form-group">
              <label>Lý do yêu cầu máu:</label>
              <textarea
                value={newRequest.reason}
                onChange={(e) =>
                  setNewRequest((prev) => ({
                    ...prev,
                    reason: e.target.value,
                  }))
                }
                placeholder="Mô tả lý do cần máu cho bệnh nhân..."
                rows="4"
                style={{ width: "100%" }}
              />
            </div>
          </div>

          <div className="auto-approve-info">
            ℹ️ Yêu cầu này sẽ được tự động duyệt với trạng thái "Hợp lệ"
          </div>

          <div className="modal-actions">
            <button
              className="btn btn-secondary"
              onClick={closeCreateModal}
              disabled={loading}
            >
              Hủy
            </button>
            <button
              className="btn btn-primary"
              onClick={handleCreateRequest}
              disabled={
                loading ||
                !newRequest.patientName ||
                !newRequest.age ||
                !newRequest.gender ||
                !newRequest.bloodGroup ||
                !newRequest.rhType ||
                !newRequest.quantity ||
                !newRequest.reason
              }
            >
              {loading ? "Đang tạo..." : "Tạo yêu cầu máu"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateBloodRequestModal;

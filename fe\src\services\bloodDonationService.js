import { apiClient } from "./axiosInstance";

/**
 * Service for Blood Donation API operations
 */
class BloodDonationService {
  /**
   * Get user information including blood donation history
   * @param {number} userId - User ID
   * @returns {Promise} API response
   */
  async getUserInfo(userId) {
    try {
      const response = await apiClient.get(`/Information/${userId}`);
      return response.data;
    } catch (error) {
      console.error("Error fetching user info:", error);
      throw error;
    }
  }

  /**
   * Get all blood donation submissions
   * @returns {Promise} API response
   */
  async getAllBloodDonationSubmissions() {
    try {
      const response = await apiClient.get("/Appointment");
      return response.data;
    } catch (error) {
      console.error("Error fetching blood donation submissions:", error);
      throw error;
    }
  }

  /**
   * Create a new blood donation submission
   * @param {Object} donationData - Donation submission data
   * @returns {Promise} API response
   */
  async createBloodDonationSubmission(donationData) {
    try {
      const response = await apiClient.post("/Appointment", donationData);
      return response.data;
    } catch (error) {
      console.error("Error creating blood donation submission:", error);
      throw error;
    }
  }

  /**
   * Get blood donation submission by appointment ID
   * @param {string} appointmentId - Appointment ID
   * @returns {Promise} API response
   */
  async getBloodDonationSubmissionById(appointmentId) {
    try {
      const response = await apiClient.get(`/Appointment/${appointmentId}`);
      return response.data;
    } catch (error) {
      console.error("Error fetching blood donation submission by ID:", error);
      throw error;
    }
  }

  /**
   * Delete blood donation submission by appointment ID
   * @param {string} appointmentId - Appointment ID
   * @returns {Promise} API response
   */
  async deleteBloodDonationSubmission(appointmentId) {
    try {
      const response = await apiClient.delete(`/Appointment/${appointmentId}`);
      return response.data;
    } catch (error) {
      console.error("Error deleting blood donation submission:", error);
      throw error;
    }
  }

  /**
   * Update blood donation submission status
   * @param {string} appointmentId - Appointment ID
   * @param {string} status - New status
   * @returns {Promise} API response
   */
  async updateBloodDonationSubmissionStatus(appointmentId, status) {
    try {
      const response = await apiClient.put(
        `/Appointment/${appointmentId}/status`,
        {
          status: status,
        }
      );
      return response.data;
    } catch (error) {
      console.error("Error updating blood donation submission status:", error);
      throw error;
    }
  }

  /**
   * Calculate age from date of birth string (YYYY-MM-DD or ISO format)
   * @param {string|Date} dateOfBirth - Date of birth
   * @returns {number} Age in years, or 0 if invalid
   */
  calculateAge(dateOfBirth) {
    if (!dateOfBirth) return 0;
    const dob = new Date(dateOfBirth);
    if (isNaN(dob)) return 0;
    const today = new Date();
    let age = today.getFullYear() - dob.getFullYear();
    const m = today.getMonth() - dob.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < dob.getDate())) {
      age--;
    }
    return age;
  }

  /**
   * Get blood donation appointments by user ID
   * @param {number} userId - User ID
   * @returns {Promise} API response
   */
  async getAppointmentsByUser(userId) {
    try {
      // First try to get user info which might contain appointment history
      try {
        const userInfo = await this.getUserInfo(userId);

        // Check if user info contains blood donation history
        if (
          userInfo.bloodDonationHistory &&
          Array.isArray(userInfo.bloodDonationHistory)
        ) {
          return userInfo.bloodDonationHistory;
        }
      } catch (userError) {
        console.log(
          "User info endpoint not available, trying blood donation endpoints"
        );
      }

      // Try multiple blood donation endpoints with includeCancelled parameter
      const possibleEndpoints = [
        `/Appointment?includeCancelled=true`, // Get all including cancelled
        `/Appointment`, // Get all and filter
        `/Appointment?userId=${userId}&includeCancelled=true`,
        `/Appointment?userId=${userId}`,
        `/Appointment?userID=${userId}&includeCancelled=true`,
        `/Appointment?userID=${userId}`,
        `/Appointment/user/${userId}?includeCancelled=true`,
        `/Appointment/user/${userId}`,
        `/Appointment/by-user/${userId}?includeCancelled=true`,
        `/Appointment/by-user/${userId}`,
        `/appointments/user/${userId}`,
      ];

      let lastError = null;

      for (const endpoint of possibleEndpoints) {
        try {
          const response = await apiClient.get(endpoint);

          // Log cancelled appointments in response
          // if (Array.isArray(response.data)) {
          //   const cancelledCount = response.data.filter(apt =>
          //     apt.Cancel === 1 || apt.cancel === 1 || apt.cancelled === true
          //   ).length;
          //   console.log(`❌ Cancelled appointments in response: ${cancelledCount}`);
          // }

          // If we get all appointments, filter by userId
          if (Array.isArray(response.data)) {
            const userAppointments = response.data.filter(
              (appointment) =>
                appointment.userId === parseInt(userId) ||
                appointment.userID === parseInt(userId) ||
                appointment.UserId === parseInt(userId) ||
                appointment.UserID === parseInt(userId)
            );

            const userCancelledCount = userAppointments.filter(
              (apt) =>
                apt.Cancel === 1 || apt.cancel === 1 || apt.cancelled === true
            ).length;
            // console.log(`👤 User appointments: ${userAppointments.length}, cancelled: ${userCancelledCount}`);

            return userAppointments;
          }

          return response.data;
        } catch (error) {
          lastError = error;
          continue;
        }
      }

      // If all endpoints fail, throw the last error
      throw lastError;
    } catch (error) {
      console.error("Error fetching blood donation appointments:", error);
      throw error;
    }
  }

  /**
   * Get specific appointment details
   * @param {number} appointmentId - Appointment ID
   * @returns {Promise} API response
   */
  async getAppointmentDetails(appointmentId) {
    try {
      const response = await apiClient.get(`/Appointment/${appointmentId}`);
      return response.data;
    } catch (error) {
      console.error("Error fetching appointment details:", error);
      throw error;
    }
  }

  /**
   * Get last donation date for a user
   * @param {number} userId - User ID
   * @returns {Promise} API response
   */
  async getLastDonation(userId) {
    try {
      const response = await apiClient.get(
        `/Appointment/last-donation/${userId}`
      );
      return response.data;
    } catch (error) {
      console.error("Error fetching last donation:", error);
      throw error;
    }
  }

  /**
   * Update self-reported donation date
   * @param {number} userId - User ID
   * @param {string} lastDonationDate - Last donation date
   * @returns {Promise} API response
   */
  async updateSelfReportedDonation(userId, lastDonationDate) {
    try {
      // Based on the memories and error messages, try the most likely format
      // The backend expects UpdateLastDonationDto structure
      let requestBody;

      if (lastDonationDate !== null && lastDonationDate !== "") {
        // User has donated before - send the date in the correct format
        // Backend expects the data wrapped in a 'dto' field
        requestBody = {
          dto: {
            selfReportedLastDonationDate: lastDonationDate,
          },
        };
      } else {
        // User hasn't donated before - send null wrapped in dto
        requestBody = {
          dto: {
            selfReportedLastDonationDate: null,
          },
        };
      }

      console.log(
        `Updating self-reported donation for user ${userId}:`,
        requestBody
      );

      const response = await apiClient.put(
        `/Information/${userId}/self-reported-donation`,
        requestBody
      );
      console.log("✅ Self-reported donation updated successfully!");
      return response.data;
    } catch (error) {
      console.error(
        "❌ Self-reported donation failed:",
        error.response?.status
      );
      console.error("Error details:", error.response?.data);

      if (error.response?.data?.errors) {
        console.error("Validation errors:", error.response.data.errors);
        Object.keys(error.response.data.errors).forEach((field) => {
          console.error(`Field '${field}':`, error.response.data.errors[field]);
        });
      }

      // Don't throw the error - let the appointment creation continue
      console.log(
        "Continuing with appointment creation despite self-reported donation failure..."
      );
      return null;
    }
  }

  /**
   * Create new blood donation appointment
   * @param {Object} appointmentData - Appointment data
   * @returns {Promise} API response
   */
  async createAppointment(appointmentData) {
    try {
      const response = await apiClient.post("/Appointment", appointmentData);
      return response.data;
    } catch (error) {
      console.error("Error creating blood donation appointment:", error);
      throw error;
    }
  }

  /**
   * Update appointment status
   * @param {number} appointmentId - Appointment ID
   * @param {string|number} status - New status
   * @param {string} notes - Doctor notes (optional)
   * @returns {Promise} API response
   */
  async updateAppointmentStatus(appointmentId, status, notes = null) {
    try {
      console.log("Updating appointment status:", {
        appointmentId,
        status,
        notes,
      });

      // Use PATCH method with correct endpoint format
      const response = await apiClient.patch(
        `/Appointment/${appointmentId}/status/${status}`
      );
      return response.data;
    } catch (error) {
      console.error("Error updating appointment status:", error);
      throw error;
    }
  }

  /**
   * Update appointment with doctor notes and health check data
   * @param {number} appointmentId - Appointment ID
   * @param {Object} updateData - Data to update (notes, health check data)
   * @returns {Promise} API response
   */
  async updateAppointmentDoctorData(appointmentId, updateData) {
    try {
      console.log("Updating appointment with doctor data:", updateData);

      const response = await apiClient.post(
        `/Appointment/doctor-update/${appointmentId}`,
        updateData
      );
      return response.data;
    } catch (error) {
      console.error("Error updating appointment doctor data:", error);
      throw error;
    }
  }

  /**
   * Update user information (weight, height, etc.)
   * @param {number} userId - User ID
   * @param {Object} updateData - Data to update (weight, height, etc.)
   * @returns {Promise} API response
   */
  async updateUserInformation(userId, updateData) {
    try {
      console.log("Updating user information:", { userId, updateData });

      const response = await apiClient.put(
        `/Information/${userId}`,
        updateData
      );
      return response.data;
    } catch (error) {
      console.error("Error updating user information:", error);
      throw error;
    }
  }

  /**
   * Get all appointments (for admin/manager)
   * @returns {Promise} API response
   */
  async getAllAppointments() {
    try {
      // Try different endpoints to get all appointments including cancelled ones
      const possibleEndpoints = [
        "/Appointment?includeCancelled=true",
        "/Appointment?includeAll=true",
        "/Appointment?status=all",
        "/Appointment?limit=100",
        "/Appointment",
      ];

      for (const endpoint of possibleEndpoints) {
        try {
          console.log(`Trying endpoint: ${endpoint}`);
          const response = await apiClient.get(endpoint);
          console.log(
            `Endpoint ${endpoint} returned ${
              response.data?.length || 0
            } appointments`
          );

          // Return the first successful response with the most data
          if (response.data && response.data.length >= 12) {
            return response.data;
          }
        } catch (endpointError) {
          console.log(`Endpoint ${endpoint} failed:`, endpointError.message);
          continue;
        }
      }

      // Fallback to basic endpoint
      const response = await apiClient.get("/Appointment");
      return response.data;
    } catch (error) {
      console.error("Error fetching all appointments:", error);
      throw error;
    }
  }

  /**
   * Cancel appointment (soft delete using PATCH method)
   * @param {number} appointmentId - Appointment ID
   * @returns {Promise} API response
   */
  async cancelAppointment(appointmentId) {
    try {
      console.log(
        "🚀 Cancelling appointment via PATCH /api/Appointment/{id}/cancel:",
        appointmentId
      );

      const response = await apiClient.patch(
        `/Appointment/${appointmentId}/cancel`
      );

      console.log("✅ Successfully cancelled appointment via PATCH method");
      return response.data;
    } catch (error) {
      console.error("❌ Error cancelling appointment:", error);
      throw error;
    }
  }

  /**
   * Delete appointment (alternative method name for backward compatibility)
   * @param {number} appointmentId - Appointment ID
   * @returns {Promise} API response
   */
  async deleteAppointment(appointmentId) {
    return this.cancelAppointment(appointmentId);
  }
}

// Create singleton instance
const bloodDonationService = new BloodDonationService();

export default bloodDonationService;

@use "../base/variables" as vars;

.notifications-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);

  .notifications-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    margin-top: 80px;

    .page-header {
      margin-bottom: 2rem;
      background: white;
      padding: 2rem;
      border-radius: 16px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      border: 1px solid #e2e8f0;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      div {
        h1 {
          color: #1e293b;
          margin-bottom: 0.5rem;
          font-size: 2.25rem;
          font-weight: 700;
          display: flex;
          align-items: center;
          gap: 0.75rem;
        }

        p {
          color: #64748b;
          font-size: 1.1rem;
          margin: 0 0 1rem 0;
          font-weight: 500;
        }

        .unread-summary {
          background: linear-gradient(135deg, #fef3c7, #fde68a);
          color: #92400e;
          padding: 0.75rem 1.25rem;
          border-radius: 12px;
          font-size: 0.95rem;
          font-weight: 600;
          border: 1px solid #fde68a;
          display: inline-flex;
          align-items: center;
          gap: 0.5rem;

          &::before {
            content: "⚠️";
            font-size: 1rem;
          }
        }
      }

      .header-actions {
        display: flex;
        gap: 0.75rem;
        align-items: center;

        .btn {
          padding: 0.75rem 1.5rem;
          border: none;
          border-radius: 12px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.2s ease;
          font-size: 0.95rem;
          white-space: nowrap;

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
          }

          &.btn-primary {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);

            &:hover:not(:disabled) {
              transform: translateY(-1px);
              box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
            }
          }

          &.btn-secondary {
            background: linear-gradient(135deg, #6b7280, #4b5563);
            color: white;
            box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);

            &:hover:not(:disabled) {
              transform: translateY(-1px);
              box-shadow: 0 6px 16px rgba(107, 114, 128, 0.4);
            }
          }
        }
      }
    }

    .filters-section {
      background: white;
      padding: 1.5rem;
      border-radius: 12px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
      border: 1px solid #e2e8f0;
      margin-bottom: 2rem;
      display: flex;
      gap: 2rem;
      align-items: center;
      flex-wrap: wrap;

      .filter-group {
        display: flex;
        align-items: center;
        gap: 0.75rem;

        label {
          font-weight: 600;
          color: #374151;
          font-size: 0.9rem;
          white-space: nowrap;
        }

        select {
          padding: 0.75rem 1rem;
          border: 2px solid #e5e7eb;
          border-radius: 8px;
          font-size: 0.95rem;
          background: white;
          transition: all 0.2s ease;
          font-weight: 500;
          min-width: 160px;
          color: #374151;

          &:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
          }

          &:hover {
            border-color: #d1d5db;
          }
        }
      }
    }

    .notifications-section {
      .loading-state {
        text-align: center;
        padding: 3rem;
        color: #6c757d;

        .loading-spinner {
          width: 40px;
          height: 40px;
          border: 4px solid #f8f9fa;
          border-top: 4px solid #28a745;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto 1rem;
        }

        p {
          font-size: 1.1rem;
          margin: 0;
        }
      }

      .empty-state {
        text-align: center;
        padding: 4rem;
        color: #6c757d;

        .empty-icon {
          font-size: 5rem;
          display: block;
          margin-bottom: 1.5rem;
        }

        h3 {
          margin: 0 0 1rem 0;
          color: #495057;
          font-size: 1.5rem;
        }

        p {
          margin: 0;
          font-size: 1.1rem;
          line-height: 1.5;
        }
      }

      .notifications-list {
        .notification-card {
          background: white;
          border-radius: 12px;
          box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
          border: 1px solid #e5e7eb;
          margin-bottom: 1rem;
          transition: all 0.2s ease;
          overflow: hidden;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
          }

          &.unread {
            border-left: 4px solid #3b82f6;
            background: linear-gradient(135deg,
                rgba(59, 130, 246, 0.02),
                rgba(147, 197, 253, 0.02));
          }

          .notification-header {
            padding: 1.25rem 1.5rem;
            background: #f8fafc;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .notification-icon-title {
              display: flex;
              align-items: center;
              gap: 0.75rem;

              .notification-icon {
                font-size: 1.5rem;
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: rgba(59, 130, 246, 0.1);
                border-radius: 10px;
              }

              .notification-title {
                font-size: 1.1rem;
                font-weight: 600;
                color: #1f2937;
                line-height: 1.4;
              }

              .unread-indicator {
                width: 8px;
                height: 8px;
                background: #3b82f6;
                border-radius: 50%;
                margin-left: 0.5rem;
              }
            }

            .notification-time {
              color: #6b7280;
              font-size: 0.85rem;
              font-weight: 500;
              white-space: nowrap;
            }
          }

          .notification-body {
            padding: 1.5rem;

            .notification-message {
              color: #374151;
              font-size: 0.95rem;
              line-height: 1.6;
              margin-bottom: 1.25rem;
            }

            .notification-details {
              background: #f1f5f9;
              padding: 1rem;
              border-radius: 8px;
              margin-bottom: 1.25rem;
              border: 1px solid #e2e8f0;

              .detail-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 0.75rem;

                &:last-child {
                  margin-bottom: 0;
                }

                strong {
                  color: #374151;
                  font-weight: 600;
                  font-size: 0.9rem;
                }

                .blood-type-badge {
                  background: linear-gradient(135deg, #ef4444, #dc2626);
                  color: white;
                  padding: 0.25rem 0.75rem;
                  border-radius: 6px;
                  font-weight: 600;
                  font-size: 0.85rem;
                  margin-left: 0.5rem;
                }
              }

              // Profile Changes Styles
              .profile-changes {
                .change-item {
                  margin-bottom: 1rem;
                  padding: 0.75rem;
                  background: #f8fafc;
                  border-radius: 6px;
                  border-left: 3px solid #3b82f6;

                  &:last-child {
                    margin-bottom: 0;
                  }

                  .change-field {
                    margin-bottom: 0.5rem;

                    strong {
                      color: #1f2937;
                      font-size: 0.9rem;
                    }
                  }

                  .change-values {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    flex-wrap: wrap;

                    .old-value {
                      background: #fee2e2;
                      color: #991b1b;
                      padding: 0.25rem 0.5rem;
                      border-radius: 4px;
                      font-size: 0.85rem;
                      font-weight: 500;
                      text-decoration: line-through;
                    }

                    .arrow {
                      color: #6b7280;
                      font-weight: 600;
                      font-size: 1rem;
                    }

                    .new-value {
                      background: #dcfce7;
                      color: #166534;
                      padding: 0.25rem 0.5rem;
                      border-radius: 4px;
                      font-size: 0.85rem;
                      font-weight: 600;
                    }
                  }
                }
              }
            }

            .notification-actions {
              display: flex;
              justify-content: space-between;
              align-items: center;
              flex-wrap: wrap;
              gap: 1rem;

              .notification-action {
                background: linear-gradient(135deg, #3b82f6, #1d4ed8);
                color: white;
                text-decoration: none;
                padding: 0.625rem 1.25rem;
                border-radius: 8px;
                font-weight: 600;
                transition: all 0.2s ease;
                box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
                font-size: 0.9rem;

                &:hover {
                  transform: translateY(-1px);
                  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
                  text-decoration: none;
                  color: white;
                }
              }

              .action-buttons {
                display: flex;
                gap: 0.75rem;

                .btn {
                  padding: 0.5rem 1rem;
                  border: none;
                  border-radius: 6px;
                  font-weight: 500;
                  cursor: pointer;
                  transition: all 0.2s ease;
                  font-size: 0.85rem;

                  &.btn-sm {
                    padding: 0.4rem 0.75rem;
                    font-size: 0.8rem;
                  }

                  &.btn-outline {
                    background: transparent;
                    border: 1.5px solid #10b981;
                    color: #10b981;

                    &:hover {
                      background: #10b981;
                      color: white;
                      transform: translateY(-1px);
                    }
                  }

                  &.btn-danger {
                    background: linear-gradient(135deg, #ef4444, #dc2626);
                    color: white;
                    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);

                    &:hover {
                      transform: translateY(-1px);
                      box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    .notification-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      margin: 2rem 0;

      .stat-card {
        background: white;
        padding: 1.5rem;
        border-radius: 12px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
        border: 1px solid #e5e7eb;
        text-align: center;
        transition: all 0.2s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        h3 {
          margin: 0 0 0.75rem 0;
          color: #6b7280;
          font-size: 0.9rem;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          font-weight: 600;
        }

        .stat-number {
          font-size: 2.25rem;
          font-weight: 700;
          margin: 0;
          color: #1f2937;

          &.unread {
            color: #f59e0b;
          }

          &.read {
            color: #10b981;
          }

          &.urgent {
            color: #ef4444;
          }
        }
      }
    }
  }
}

// Animations
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

// Responsive
@media (max-width: 768px) {
  .notifications-page {
    .notifications-content {
      padding: 1rem;
      margin-top: 70px;

      .page-header {
        padding: 1.5rem;
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;

        div h1 {
          font-size: 1.875rem;
        }

        .header-actions {
          flex-direction: column;
          gap: 0.5rem;

          .btn {
            width: 100%;
            text-align: center;
          }
        }
      }

      .filters-section {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;

        .filter-group {
          flex-direction: column;
          align-items: stretch;
          gap: 0.5rem;

          select {
            min-width: auto;
          }
        }
      }

      .notifications-section .notifications-list .notification-card {
        .notification-header {
          padding: 1rem;
          flex-direction: column;
          align-items: flex-start;
          gap: 0.75rem;

          .notification-icon-title {
            .notification-icon {
              width: 36px;
              height: 36px;
              font-size: 1.25rem;
            }
          }
        }

        .notification-body {
          padding: 1rem;

          .notification-actions {
            flex-direction: column;
            align-items: stretch;
            gap: 0.75rem;

            .notification-action {
              text-align: center;
            }

            .action-buttons {
              justify-content: stretch;

              .btn {
                flex: 1;
              }
            }
          }
        }
      }

      .notification-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;

        .stat-card {
          padding: 1rem;

          .stat-number {
            font-size: 1.875rem;
          }
        }
      }
    }
  }
}
import axiosInstance from "./axiosInstance";

const DASHBOARD_API = import.meta.env.VITE_DASHBOARD_API || "/api/Dashboard";
const BLOOD_REQUEST_API = import.meta.env.VITE_BLOOD_REQUEST_API || "/api/BloodRequest";
const BLOOD_INVENTORY_API = import.meta.env.VITE_BLOOD_INVENTORY_API || "/api/BloodInventory";

/**
 * Service for Doctor Dashboard API operations
 */
const doctorDashboardService = {
  /**
   * Get dashboard statistics for doctor
   * @param {number} doctorId - Doctor ID
   * @returns {Promise} API response
   */
  getDashboardStats: async (doctorId) => {
    try {
      const response = await axiosInstance.get(`${DASHBOARD_API}/doctor/${doctorId}`);
      return {
        success: true,
        data: response.data,
        message: "Lấy thống kê dashboard thành công",
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || "C<PERSON> lỗi xảy ra khi lấy thống kê dashboard",
        details: error.response?.data,
      };
    }
  },

  /**
   * Get blood requests statistics for doctor
   * @param {number} doctorId - Doctor ID
   * @returns {Promise} API response
   */
  getBloodRequestStats: async (doctorId) => {
    try {
      const response = await axiosInstance.get(`${BLOOD_REQUEST_API}/stats/doctor/${doctorId}`);
      return {
        success: true,
        data: response.data,
        message: "Lấy thống kê yêu cầu máu thành công",
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || "Có lỗi xảy ra khi lấy thống kê yêu cầu máu",
        details: error.response?.data,
      };
    }
  },

  /**
   * Get blood inventory statistics
   * @returns {Promise} API response
   */
  getBloodInventoryStats: async () => {
    try {
      const response = await axiosInstance.get(`${BLOOD_INVENTORY_API}/stats`);
      return {
        success: true,
        data: response.data,
        message: "Lấy thống kê kho máu thành công",
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || "Có lỗi xảy ra khi lấy thống kê kho máu",
        details: error.response?.data,
      };
    }
  },

  /**
   * Get monthly blood requests trend
   * @param {number} months - Number of months to get (default: 6)
   * @returns {Promise} API response
   */
  getMonthlyRequestsTrend: async (months = 6) => {
    try {
      const response = await axiosInstance.get(`${BLOOD_REQUEST_API}/trend/monthly?months=${months}`);
      return {
        success: true,
        data: response.data,
        message: "Lấy xu hướng yêu cầu máu theo tháng thành công",
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || "Có lỗi xảy ra khi lấy xu hướng yêu cầu máu",
        details: error.response?.data,
      };
    }
  },

  /**
   * Get recent blood requests for doctor
   * @param {number} doctorId - Doctor ID
   * @param {number} limit - Number of recent requests (default: 5)
   * @returns {Promise} API response
   */
  getRecentBloodRequests: async (doctorId, limit = 5) => {
    try {
      const response = await axiosInstance.get(`${BLOOD_REQUEST_API}/recent/doctor/${doctorId}?limit=${limit}`);
      return {
        success: true,
        data: response.data,
        message: "Lấy yêu cầu máu gần đây thành công",
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || "Có lỗi xảy ra khi lấy yêu cầu máu gần đây",
        details: error.response?.data,
      };
    }
  },

  /**
   * Get notifications for doctor
   * @param {number} doctorId - Doctor ID
   * @returns {Promise} API response
   */
  getNotifications: async (doctorId) => {
    try {
      const response = await axiosInstance.get(`${DASHBOARD_API}/notifications/doctor/${doctorId}`);
      return {
        success: true,
        data: response.data,
        message: "Lấy thông báo thành công",
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || "Có lỗi xảy ra khi lấy thông báo",
        details: error.response?.data,
      };
    }
  },

  /**
   * Mark notification as read
   * @param {number} notificationId - Notification ID
   * @returns {Promise} API response
   */
  markNotificationAsRead: async (notificationId) => {
    try {
      const response = await axiosInstance.patch(`${DASHBOARD_API}/notifications/${notificationId}/read`);
      return {
        success: true,
        data: response.data,
        message: "Đánh dấu thông báo đã đọc thành công",
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || "Có lỗi xảy ra khi đánh dấu thông báo",
        details: error.response?.data,
      };
    }
  },
};

export default doctorDashboardService;

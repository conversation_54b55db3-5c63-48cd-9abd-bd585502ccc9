import React, { useState, useEffect, useCallback } from "react";
import {
  Table,
  Card,
  Button,
  Select,
  Input,
  Space,
  Tooltip,
  message,
  Row,
  Col,
  Statistic,
  Tabs,
  Typography,
  DatePicker,
} from "antd";
import {
  ReloadOutlined,
  EyeOutlined,
  ExportOutlined,
  CalendarOutlined,
} from "@ant-design/icons";
import ManagerSidebar from "../../components/manager/ManagerSidebar";
import ManagerBloodRequestDetailModal from "../../components/manager/ManagerBloodRequestDetailModal";
import ManagerBloodCheckOutModal from "../../components/manager/blood-inventory/ManagerBloodCheckOutModal";
import PageHeader from "../../components/manager/PageHeader";
import { REQUEST_STATUS, URGENCY_LEVELS } from "../../services/mockData";
import { bloodRequestService } from "../../services/bloodRequestService";
import {
  classifyBloodRequest,
  isValidUserID,
} from "../../utils/bloodRequestClassification";
import { getStatusText, getStatusColor } from "../../utils/bloodRequestUtils";
import userInfoService from "../../services/userInfoService";
import {
  checkOutBloodInventory,
  fetchBloodInventory,
} from "../../services/bloodInventoryService";
import authService from "../../services/authService";
import {
  getBloodComponentId,
  getBloodComponentName,
} from "../../constants/bloodInventoryConstants";
import "../../styles/pages/BloodRequestsManagement.scss";
import "../../styles/components/PageHeader.scss";
import "../../styles/components/BloodRequestTable.scss";

// Blood types for filter
const BLOOD_TYPES = ["A+", "A-", "B+", "B-", "AB+", "AB-", "O+", "O-"];

const BloodRequestsPage = () => {
  const [allRequests, setAllRequests] = useState([]);
  const [internalRequests, setInternalRequests] = useState([]); // Requests from doctors
  const [externalRequests, setExternalRequests] = useState([]); // Requests from members
  const [filteredRequests, setFilteredRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("internal");
  const [inventory, setInventory] = useState([]); // Add inventory state
  const [filters, setFilters] = useState({
    status: "all",
    bloodType: "all",
    patientName: "",
    requestId: "", // Filter theo mã yêu cầu
    dateRange: null, // Filter theo khoảng ngày tạo
  });
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [exportModalVisible, setExportModalVisible] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [exportForm, setExportForm] = useState({
    bloodGroup: "",
    rhType: "",
    componentType: "",
    bagType: "",
    quantity: 0,
    notes: "",
  });

  // Define applyFilters function first
  const applyFilters = useCallback(() => {
    // Get requests from current tab
    const currentRequests =
      activeTab === "internal" ? internalRequests : externalRequests;
    let filtered = [...currentRequests];

    // Filter by status
    if (filters.status !== "all") {
      filtered = filtered.filter((req) => req.status === filters.status);
    }

    // Filter by blood type
    if (filters.bloodType !== "all") {
      filtered = filtered.filter(
        (req) => req.bloodTypeDisplay === filters.bloodType
      );
    }

    // Filter by patient name
    if (filters.patientName.trim()) {
      const searchTerm = filters.patientName.toLowerCase().trim();
      filtered = filtered.filter(
        (req) =>
          req.patientName.toLowerCase().includes(searchTerm) ||
          req.requestCode.toLowerCase().includes(searchTerm)
      );
    }

    // Filter by request ID
    if (filters.requestId.trim()) {
      const searchTerm = filters.requestId.toLowerCase().trim();
      filtered = filtered.filter(
        (req) =>
          req.requestID?.toString().includes(searchTerm) ||
          req.requestCode?.toLowerCase().includes(searchTerm)
      );
    }

    // Filter by date range
    if (filters.dateRange && filters.dateRange.length === 2) {
      const [startDate, endDate] = filters.dateRange;
      filtered = filtered.filter((req) => {
        const createdDate = new Date(req.createdTime || req.createdAt);
        return (
          createdDate >= startDate.startOf("day").toDate() &&
          createdDate <= endDate.endOf("day").toDate()
        );
      });
    }

    // Tự động sắp xếp theo mã yêu cầu (tăng dần - yêu cầu cũ trước)
    filtered.sort((a, b) => {
      const aId = parseInt(a.requestID) || 0;
      const bId = parseInt(b.requestID) || 0;
      return aId - bId;
    });

    setFilteredRequests(filtered);
  }, [internalRequests, externalRequests, activeTab, filters]);

  // Load inventory data (giống hệt BloodInventoryManagement.jsx)
  const loadInventory = async () => {
    try {
      const data = await fetchBloodInventory();

      // Transform data giống hệt BloodInventoryManagement.jsx
      const inventoryWithComponentType = data.map((item) => {
        return {
          ...item,
          componentType: getBloodComponentName(item.componentId),
          inventoryId: item.InventoryID,
        };
      });

      setInventory(inventoryWithComponentType);
    } catch (error) {
      console.error("Error loading inventory:", error);
      setInventory([]);
    }
  };

  useEffect(() => {
    loadBloodRequests();
    loadInventory(); // Also load inventory
  }, []);

  // Apply filters when data or filters change
  useEffect(() => {
    applyFilters();
  }, [applyFilters]);

  const loadBloodRequests = async () => {
    setLoading(true);
    try {
      // Call the real API using bloodRequestService
      const response = await bloodRequestService.getBloodRequests();

      if (response.success) {
        // Transform API data to match UI expectations
        const enhancedRequests = response.data.map((request) => {
          // Handle rhType formatting (API might return "Rh+" or just "+")
          const formatRhType = (rhType) => {
            if (!rhType) return "+";
            if (rhType.includes("+")) return "+";
            if (rhType.includes("-")) return "-";
            return "+"; // default
          };

          return {
            ...request,
            requestID: request.requestId || request.requestID,
            userID: request.userId || request.userID, // Fix: API might return userId instead of userID
            requestCode: request.requestId
              ? `BR-${String(request.requestId).padStart(6, "0")}`
              : `BR-${String(request.requestID || 0).padStart(6, "0")}`,
            bloodTypeDisplay: `${request.bloodGroup || "O"}${formatRhType(
              request.rhType
            )}`,
            quantityUnit: `${request.quantity || 0} đơn vị`,
            createdAt:
              request.createdTime ||
              request.createdAt ||
              new Date().toISOString(),
            updatedAt: request.updatedAt || new Date().toISOString(),
            urgencyLevel: request.urgencyLevel || URGENCY_LEVELS.NORMAL,
            priorityLevel: request.urgencyLevel || URGENCY_LEVELS.NORMAL,
            canExport:
              request.status === REQUEST_STATUS.ACCEPTED ||
              request.status === REQUEST_STATUS.PROCESSING,
            // Additional fields for display
            requesterName: request.doctorName || "Chưa cập nhật",
            department: "Chưa cập nhật", // API doesn't provide department info
            contactInfo: request.doctorPhone || "Chưa cập nhật",
          };
        });

        // Separate requests by user role - Manager can see all requests
        const internalRequestsList = [];
        const externalRequestsList = [];

        // Get user info for each request to determine role
        for (const request of enhancedRequests) {
          try {
            // Use classification utility to determine request type
            const classification = classifyBloodRequest(request);

            // Check if userID is valid before making API call
            if (!isValidUserID(request.userID)) {
              // Use classification result
              if (classification.isInternal) {
                internalRequestsList.push({
                  ...request,
                  userRole: classification.userRole,
                  userRoleID: classification.userRoleID,
                });
              } else {
                externalRequestsList.push({
                  ...request,
                  userRole: classification.userRole,
                  userRoleID: classification.userRoleID,
                });
              }
              continue;
            }

            const userInfo = await userInfoService.getUserInfo(request.userID);

            // roleID = 1: Member (External requests)
            // roleID = 2: Staff-Doctor (Internal requests)
            if (userInfo.roleID === 1) {
              externalRequestsList.push({
                ...request,
                userRole: "Member",
                userRoleID: 1,
              });
            } else if (userInfo.roleID === 2) {
              internalRequestsList.push({
                ...request,
                userRole: "Staff-Doctor",
                userRoleID: 2,
              });
            } else {
              // Default to external for other roles
              externalRequestsList.push({
                ...request,
                userRole: "Other",
                userRoleID: userInfo.roleID,
              });
            }
          } catch (error) {
            // Use classification utility for fallback
            const classification = classifyBloodRequest(request);

            if (classification.isInternal) {
              internalRequestsList.push({
                ...request,
                userRole: classification.userRole,
                userRoleID: classification.userRoleID,
              });
            } else {
              externalRequestsList.push({
                ...request,
                userRole: classification.userRole,
                userRoleID: classification.userRoleID,
              });
            }
          }
        }

        // Sort both lists by priority and creation date
        const sortRequests = (requests) => {
          return requests.sort((a, b) => {
            // First sort by priority (urgent first)
            if (a.priorityLevel !== b.priorityLevel) {
              return b.priorityLevel - a.priorityLevel;
            }
            // Then by creation date (newest first)
            return new Date(b.createdAt) - new Date(a.createdAt);
          });
        };

        const sortedInternalRequests = sortRequests(internalRequestsList);
        const sortedExternalRequests = sortRequests(externalRequestsList);
        const allSortedRequests = [
          ...sortedInternalRequests,
          ...sortedExternalRequests,
        ];

        setInternalRequests(sortedInternalRequests);
        setExternalRequests(sortedExternalRequests);
        setAllRequests(allSortedRequests);
      } else {
        message.error(
          response.error || "Có lỗi xảy ra khi tải danh sách yêu cầu!"
        );
        setAllRequests([]);
        setInternalRequests([]);
        setExternalRequests([]);
      }
    } catch (error) {
      message.error("Có lỗi xảy ra khi tải danh sách yêu cầu!");
      setAllRequests([]);
      setInternalRequests([]);
      setExternalRequests([]);
    } finally {
      setLoading(false);
    }
  };

  const handleStatusUpdate = async (requestId, newStatus) => {
    try {
      // Use PATCH API to update status with proper notes
      let notes = "";
      const currentUser = authService.getCurrentUser();

      if (newStatus === REQUEST_STATUS.ACCEPTED) {
        notes = `Yêu cầu được chấp nhận bởi ${currentUser?.name || "Manager"}`;
      } else if (newStatus === REQUEST_STATUS.COMPLETED) {
        notes = `Máu đã được xuất kho và hoàn thành bởi ${
          currentUser?.name || "Manager"
        }`;
      } else if (newStatus === REQUEST_STATUS.REJECTED) {
        notes = `Yêu cầu bị từ chối bởi ${currentUser?.name || "Manager"}`;
      } else {
        notes = `Trạng thái được cập nhật bởi ${
          currentUser?.name || "Manager"
        }`;
      }

      // Call PATCH API to update status
      const apiResponse = await bloodRequestService.updateBloodRequestStatus(
        requestId,
        newStatus,
        notes
      );

      if (apiResponse.success) {
        // Update local state only after API success
        const currentTime = new Date().toISOString();
        const updateRequest = (req) => {
          if (req.requestID === requestId) {
            const updatedReq = {
              ...req,
              status: newStatus,
              updatedAt: currentTime,
            };

            // Add specific timestamp fields based on status
            if (newStatus === REQUEST_STATUS.ACCEPTED) {
              updatedReq.acceptedAt = currentTime;
              updatedReq.acceptedBy = currentUser?.name;
              updatedReq.acceptedNotes = notes;
            } else if (newStatus === REQUEST_STATUS.COMPLETED) {
              updatedReq.completedAt = currentTime;
              updatedReq.completedBy = currentUser?.name;
              updatedReq.completedNotes = notes;
            } else if (newStatus === REQUEST_STATUS.REJECTED) {
              updatedReq.rejectedAt = currentTime;
              updatedReq.rejectedBy = currentUser?.name;
              updatedReq.rejectedNotes = notes;
            }

            return updatedReq;
          }
          return req;
        };

        // Update all request states
        setAllRequests((prev) => prev.map(updateRequest));
        setInternalRequests((prev) => prev.map(updateRequest));
        setExternalRequests((prev) => prev.map(updateRequest));

        message.success(
          apiResponse.message || "Cập nhật trạng thái thành công!"
        );
      } else {
        throw new Error(apiResponse.error || "API call failed");
      }
    } catch (error) {
      console.error("Error updating request status:", error);
      message.error(`Có lỗi xảy ra khi cập nhật trạng thái: ${error.message}`);
    }
  };

  const handleViewDetails = (request) => {
    setSelectedRequest(request);
    setDetailModalVisible(true);
  };

  const handleExportBlood = (request) => {
    // Set selected request and pre-fill form with request data
    setSelectedRequest(request);

    // Parse blood type from request (e.g., "A+" -> bloodGroup: "A", rhType: "Rh+")
    const bloodTypeDisplay = request.bloodTypeDisplay || "";
    let bloodGroup = "";
    let rhType = "";

    if (bloodTypeDisplay.includes("+")) {
      bloodGroup = bloodTypeDisplay.replace("+", "");
      rhType = "Rh+";
    } else if (bloodTypeDisplay.includes("-")) {
      bloodGroup = bloodTypeDisplay.replace("-", "");
      rhType = "Rh-";
    }

    // Pre-fill export form with request data
    setExportForm({
      bloodGroup: bloodGroup,
      rhType: rhType,
      componentType: "Toàn phần", // Default to whole blood (Vietnamese name)
      bagType: "450ml", // Default bag type
      quantity: request.quantity || 1,
      notes: `Xuất kho cho yêu cầu ${request.requestCode} - Bệnh nhân: ${request.patientName}`,
    });

    // Open export modal
    setExportModalVisible(true);
  };

  const handleConfirmExport = async () => {
    // Validation
    if (
      !exportForm.bloodGroup ||
      !exportForm.rhType ||
      !exportForm.componentType ||
      !exportForm.quantity ||
      exportForm.quantity <= 0
    ) {
      message.error("Vui lòng điền đầy đủ thông tin hợp lệ!");
      return;
    }

    setExportLoading(true);
    try {
      // Get current user info
      const userId = authService.getCurrentUser()?.id;
      if (!userId) {
        throw new Error("Không tìm thấy thông tin người dùng");
      }

      // Create payload for blood inventory API (same format as BloodInventoryManagement.jsx)
      const componentId = getBloodComponentId(exportForm.componentType);

      // Validate componentId
      if (!componentId || componentId === 0) {
        throw new Error(
          `Thành phần máu không hợp lệ: "${exportForm.componentType}". Vui lòng chọn lại.`
        );
      }

      // Kiểm tra inventory đã load chưa
      if (!inventory || inventory.length === 0) {
        throw new Error("Dữ liệu kho máu chưa được tải. Vui lòng thử lại sau.");
      }

      // Kiểm tra số lượng tồn kho trước khi xuất (giống BloodInventoryManagement.jsx)
      const selected = inventory.find(
        (item) =>
          item.bloodGroup === exportForm.bloodGroup &&
          item.rhType === exportForm.rhType &&
          item.componentType === exportForm.componentType
      );

      if (!selected) {
        Modal.warning({
          title: "⚠️ Cảnh báo: Không tìm thấy kho máu phù hợp",
          content: (
            <div>
              <p>
                <strong>Nhóm máu yêu cầu:</strong> {exportForm.bloodGroup}
                {exportForm.rhType === "Rh+" ? "+" : "-"}
              </p>
              <p>
                <strong>Thành phần:</strong> {exportForm.componentType}
              </p>
              <br />
              <p style={{ color: "#ff4d4f" }}>
                Không tìm thấy loại máu phù hợp trong kho. Vui lòng kiểm tra lại
                thông tin hoặc liên hệ bộ phận quản lý kho máu.
              </p>
            </div>
          ),
          okText: "Đã hiểu",
          width: 500,
        });
        setExportLoading(false);
        return;
      }

      if (selected.quantity < parseInt(exportForm.quantity)) {
        // Hiển thị modal cảnh báo thay vì chỉ throw error
        Modal.warning({
          title: "⚠️ Cảnh báo: Số lượng máu không đủ",
          content: (
            <div>
              <p>
                <strong>Yêu cầu xuất:</strong> {exportForm.quantity} đơn vị
              </p>
              <p>
                <strong>Số lượng tồn kho:</strong> {selected.quantity} đơn vị
              </p>
              <p>
                <strong>Thiếu:</strong>{" "}
                {parseInt(exportForm.quantity) - selected.quantity} đơn vị
              </p>
              <br />
              <p style={{ color: "#ff4d4f" }}>
                Không thể thực hiện xuất kho do số lượng tồn kho không đủ. Vui
                lòng kiểm tra lại hoặc liên hệ bộ phận quản lý kho máu.
              </p>
            </div>
          ),
          okText: "Đã hiểu",
          width: 500,
        });
        setExportLoading(false);
        return; // Dừng thao tác xuất kho
      }

      const payload = {
        bloodGroup: exportForm.bloodGroup,
        rhType: exportForm.rhType,
        componentId: componentId, // Convert component name to ID
        quantity: parseInt(exportForm.quantity),
        bagType: exportForm.bagType || "450ml", // Default bag type
        notes:
          exportForm.notes ||
          `Xuất kho cho yêu cầu ${selectedRequest.requestCode} - Bệnh nhân: ${selectedRequest.patientName}`,
        performedBy: parseInt(userId),
      };

      // Call blood inventory checkout API
      await checkOutBloodInventory(payload);

      // Cập nhật UI inventory sau khi thành công (giống BloodInventoryManagement.jsx)
      setInventory((prev) =>
        prev.map((item) => {
          if (
            item.bloodGroup === exportForm.bloodGroup &&
            item.rhType === exportForm.rhType &&
            item.componentType === exportForm.componentType
          ) {
            return {
              ...item,
              quantity: Math.max(
                0,
                item.quantity - parseInt(exportForm.quantity)
              ),
            };
          }
          return item;
        })
      );

      // Update request status to completed
      await handleStatusUpdate(
        selectedRequest.requestID,
        REQUEST_STATUS.COMPLETED
      );

      message.success(
        `Xuất kho thành công! Đã xuất ${exportForm.quantity} đơn vị máu ${
          exportForm.bloodGroup
        }${exportForm.rhType === "Rh+" ? "+" : "-"} cho yêu cầu ${
          selectedRequest.requestCode
        }`
      );

      // Close modal and reset form
      setExportModalVisible(false);
      setSelectedRequest(null);
      setExportForm({
        bloodGroup: "",
        rhType: "",
        componentType: "",
        bagType: "",
        quantity: 0,
        notes: "",
      });

      // No need to reload - handleStatusUpdate already updated all states
    } catch (error) {
      console.error("Export error:", error);
      const errorMessage =
        error.response?.data?.message || error.message || "Lỗi không xác định";
      message.error(`Xuất kho thất bại: ${errorMessage}`);
    } finally {
      setExportLoading(false);
    }
  };

  // Table columns configuration - Doctor Style
  const tableColumns = [
    {
      title: "Mã yêu cầu",
      dataIndex: "requestID",
      key: "requestID",
      width: 100,
      render: (id) => <span className="request-id">#{id}</span>,
    },
    {
      title: "Bệnh nhân",
      dataIndex: "patientName",
      key: "patientName",
      width: 150,
      render: (patientName, record) => (
        <div className="patient-info">
          <div className="patient-name">{patientName}</div>
          <div className="patient-details">
            <span className="detail-item">{record.age} tuổi</span>
            <span className="detail-item">{record.gender}</span>
          </div>
        </div>
      ),
    },
    {
      title: "Nhóm máu",
      dataIndex: "bloodTypeDisplay",
      key: "bloodTypeDisplay",
      width: 80,
      render: (bloodType) => {
        const isPositive = bloodType?.includes("+");
        return (
          <span
            className={`blood-type-tag ${isPositive ? "positive" : "negative"}`}
          >
            {bloodType}
          </span>
        );
      },
    },
    {
      title: "Số lượng",
      dataIndex: "quantity",
      key: "quantity",
      width: 80,
      render: (quantity) => (
        <span className="quantity-display">
          {quantity}
          <span className="unit">ml</span>
        </span>
      ),
    },
    {
      title: activeTab === "external" ? "Người yêu cầu" : "Bác sĩ",
      dataIndex: "requesterName",
      key: "requesterName",
      width: 120,
      render: (requesterName, record) => (
        <div className="doctor-info">
          <div className="doctor-name">{requesterName || "N/A"}</div>
          {record.department && (
            <div className="doctor-department">{record.department}</div>
          )}
        </div>
      ),
    },
    {
      title: "Ngày tạo",
      dataIndex: "createdAt",
      key: "createdAt",
      width: 110,
      render: (time) => (
        <div className="date-display">
          <CalendarOutlined className="date-icon" />
          {new Date(time).toLocaleDateString("vi-VN")}
        </div>
      ),
    },
    {
      title: "Trạng thái",
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (status) => (
        <span className={`status-badge status-${getStatusColor(status)}`}>
          {getStatusText(status)}
        </span>
      ),
    },
    {
      title: "Hành động",
      key: "actions",
      width: 120,
      render: (_, record) => {
        const actionButtonStyle = {
          borderRadius: "8px",
          border: "none",
          boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
          transition: "all 0.2s ease",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        };

        const viewButtonStyle = {
          ...actionButtonStyle,
          background: "#e6f7ff",
          color: "#1890ff",
          border: "1px solid #91d5ff",
        };

        const acceptButtonStyle = {
          ...actionButtonStyle,
          background: "#f6ffed",
          color: "#52c41a",
          border: "1px solid #b7eb8f",
        };

        return (
          <Space size="small" className="action-buttons">
            {/* Always show Details button */}
            <Tooltip title="Xem chi tiết">
              <Button
                icon={<EyeOutlined />}
                onClick={() => handleViewDetails(record)}
                size="small"
                style={viewButtonStyle}
                className="view-btn"
              />
            </Tooltip>

            {/* Show export button only for accepted requests (status = 1) */}
            {record.status === REQUEST_STATUS.ACCEPTED && (
              <Tooltip title="Xuất kho">
                <Button
                  icon={<ExportOutlined />}
                  onClick={() => handleExportBlood(record)}
                  size="small"
                  style={acceptButtonStyle}
                  className="export-btn"
                >
                  Xuất kho
                </Button>
              </Tooltip>
            )}
          </Space>
        );
      },
    },
  ];

  return (
    <div className="blood-requests-management">
      <ManagerSidebar />

      <div className="blood-requests-content">
        <PageHeader
          title="Quản lý Yêu cầu Máu"
          description="Xử lý và theo dõi tất cả yêu cầu máu từ bác sĩ và bệnh nhân"
          icon={CalendarOutlined}
          actions={[
            {
              label: "Làm mới",
              icon: <ReloadOutlined />,
              onClick: loadBloodRequests,
              loading: loading,
            },
          ]}
        />

        {/* Tabs for separating internal and external requests */}
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          style={{ marginBottom: 16 }}
          items={[
            {
              key: "internal",
              label: `🏥 Yêu cầu máu nội bộ (${internalRequests.length})`,
            },
            {
              key: "external",
              label: `🌐 Yêu cầu máu từ bên ngoài (${externalRequests.length})`,
            },
          ]}
        />

        {/* Statistics Cards - Doctor Style */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="Tổng yêu cầu"
                value={
                  activeTab === "internal"
                    ? internalRequests.length
                    : externalRequests.length
                }
                valueStyle={{ color: "#1677ff", fontWeight: 600 }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="Chờ duyệt"
                value={
                  (activeTab === "internal"
                    ? internalRequests
                    : externalRequests
                  ).filter((r) => r.status === REQUEST_STATUS.PENDING).length
                }
                valueStyle={{ color: "#faad14", fontWeight: 600 }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="Đã chấp nhận"
                value={
                  (activeTab === "internal"
                    ? internalRequests
                    : externalRequests
                  ).filter((r) => r.status === REQUEST_STATUS.ACCEPTED).length
                }
                valueStyle={{ color: "#52c41a", fontWeight: 600 }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="Hoàn thành"
                value={
                  (activeTab === "internal"
                    ? internalRequests
                    : externalRequests
                  ).filter((r) => r.status === REQUEST_STATUS.COMPLETED).length
                }
                valueStyle={{ color: "#1890ff", fontWeight: 600 }}
              />
            </Card>
          </Col>
        </Row>

        {/* Filters - Doctor Style */}
        <Card style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col xs={24} sm={8} md={6}>
              <div style={{ marginBottom: 8 }}>
                <label
                  style={{ fontSize: "14px", fontWeight: 500, color: "#333" }}
                >
                  Nhóm máu:
                </label>
              </div>
              <Select
                value={filters.bloodType}
                onChange={(value) =>
                  setFilters({ ...filters, bloodType: value })
                }
                style={{ width: "100%" }}
                placeholder="Chọn nhóm máu"
              >
                <Select.Option value="all">Tất cả</Select.Option>
                {BLOOD_TYPES.map((type) => (
                  <Select.Option key={type} value={type}>
                    {type}
                  </Select.Option>
                ))}
              </Select>
            </Col>
            <Col xs={24} sm={8} md={6}>
              <div style={{ marginBottom: 8 }}>
                <label
                  style={{ fontSize: "14px", fontWeight: 500, color: "#333" }}
                >
                  Trạng thái:
                </label>
              </div>
              <Select
                value={filters.status}
                onChange={(value) => setFilters({ ...filters, status: value })}
                style={{ width: "100%" }}
                placeholder="Chọn trạng thái"
              >
                <Select.Option value="all">Tất cả</Select.Option>
                <Select.Option value={REQUEST_STATUS.PENDING}>
                  Đang chờ xử lý
                </Select.Option>
                <Select.Option value={REQUEST_STATUS.ACCEPTED}>
                  Đã chấp nhận
                </Select.Option>
                <Select.Option value={REQUEST_STATUS.PROCESSING}>
                  Đang xử lý
                </Select.Option>
                <Select.Option value={REQUEST_STATUS.COMPLETED}>
                  Hoàn thành
                </Select.Option>
                <Select.Option value={REQUEST_STATUS.REJECTED}>
                  Từ chối
                </Select.Option>
              </Select>
            </Col>
            <Col xs={24} sm={8} md={6}>
              <div style={{ marginBottom: 8 }}>
                <label
                  style={{ fontSize: "14px", fontWeight: 500, color: "#333" }}
                >
                  Tìm kiếm:
                </label>
              </div>
              <Input
                placeholder="Tên bệnh nhân hoặc mã yêu cầu"
                value={filters.patientName}
                onChange={(e) =>
                  setFilters({ ...filters, patientName: e.target.value })
                }
                style={{ width: "100%" }}
              />
            </Col>
            <Col xs={24} sm={24} md={6}>
              <div style={{ marginBottom: 8 }}>
                <label
                  style={{ fontSize: "14px", fontWeight: 500, color: "#333" }}
                >
                  &nbsp;
                </label>
              </div>
              <div style={{ textAlign: "right" }}>
                <Typography.Text type="secondary">
                  Hiển thị {filteredRequests.length} /{" "}
                  {activeTab === "internal"
                    ? internalRequests.length
                    : externalRequests.length}{" "}
                  yêu cầu
                </Typography.Text>
              </div>
            </Col>
          </Row>

          {/* Bộ lọc nâng cao */}
          <Row gutter={16} style={{ marginTop: 16 }}>
            <Col xs={24} sm={8} md={6}>
              <div style={{ marginBottom: 8 }}>
                <label
                  style={{ fontSize: "14px", fontWeight: 500, color: "#333" }}
                >
                  Mã yêu cầu:
                </label>
              </div>
              <Input
                placeholder="Nhập mã yêu cầu"
                value={filters.requestId}
                onChange={(e) =>
                  setFilters({ ...filters, requestId: e.target.value })
                }
                style={{ width: "100%" }}
              />
            </Col>
            <Col xs={24} sm={16} md={12}>
              <div style={{ marginBottom: 8 }}>
                <label
                  style={{ fontSize: "14px", fontWeight: 500, color: "#333" }}
                >
                  Khoảng ngày tạo:
                </label>
              </div>
              <DatePicker.RangePicker
                value={filters.dateRange}
                onChange={(dates) =>
                  setFilters({ ...filters, dateRange: dates })
                }
                style={{ width: "100%" }}
                format="DD/MM/YYYY"
                placeholder={["Từ ngày", "Đến ngày"]}
              />
            </Col>
            <Col xs={24} sm={24} md={6}>
              <div style={{ marginBottom: 8 }}>
                <label
                  style={{ fontSize: "14px", fontWeight: 500, color: "#333" }}
                >
                  &nbsp;
                </label>
              </div>
              <Button
                onClick={() =>
                  setFilters({
                    status: "all",
                    bloodType: "all",
                    patientName: "",
                    requestId: "",
                    dateRange: null,
                  })
                }
                style={{ width: "100%" }}
              >
                Xóa bộ lọc
              </Button>
            </Col>
          </Row>
        </Card>

        {/* Data Display - Doctor Style */}
        <Table
          className="blood-request-table"
          columns={tableColumns}
          dataSource={filteredRequests}
          rowKey="requestID"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} của ${total} yêu cầu`,
          }}
        />

        {/* Detail Modal - Manager Style */}
        <ManagerBloodRequestDetailModal
          isOpen={detailModalVisible}
          onClose={() => setDetailModalVisible(false)}
          request={selectedRequest}
          onUpdate={handleStatusUpdate}
        />

        {/* Export Blood Modal */}
        {exportModalVisible && (
          <ManagerBloodCheckOutModal
            open={exportModalVisible}
            onOk={handleConfirmExport}
            onCancel={() => {
              setExportModalVisible(false);
              setSelectedRequest(null);
              setExportForm({
                bloodGroup: "",
                rhType: "",
                componentType: "",
                bagType: "",
                quantity: 0,
                notes: "",
              });
            }}
            confirmLoading={exportLoading}
            form={exportForm}
            setForm={setExportForm}
          />
        )}
      </div>
    </div>
  );
};

export default BloodRequestsPage;

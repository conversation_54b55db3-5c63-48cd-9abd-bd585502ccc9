import { DONATION_STATUS, REQUEST_STATUS } from "../constants/systemConstants";

/**
 * Helper function to safely format date - giữ nguyên logic gốc
 */
export const formatDate = (dateValue) => {
  if (!dateValue) return "Chưa xác định";

  const date = new Date(dateValue);
  if (isNaN(date.getTime())) return "Chưa xác định";

  return date.toLocaleDateString("vi-VN");
};

/**
 * Helper function to get status info for display - giữ nguyên logic gốc
 */
export const getStatusInfo = (status, type) => {
  const donationStatusMap = {
    [DONATION_STATUS.REGISTERED]: { text: "Đã đăng ký", color: "orange" },
    [DONATION_STATUS.HEALTH_CHECKED]: {
      text: "Đã chấp nhận",
      color: "green",
    },
    [DONATION_STATUS.NOT_ELIGIBLE_HEALTH]: {
      text: "Không chấp nhận",
      color: "red",
    },
    [DONATION_STATUS.DONATED]: { text: "Đã hiến máu", color: "purple" },
    [DONATION_STATUS.BLOOD_TESTED]: { text: "Đã xét nghiệm", color: "blue" },
    [DONATION_STATUS.NOT_ELIGIBLE_TEST]: {
      text: "Không đủ điều kiện xét nghiệm",
      color: "red",
    },
    [DONATION_STATUS.COMPLETED]: { text: "Hoàn thành", color: "green" },
    [DONATION_STATUS.STORED]: { text: "Đã nhập kho", color: "cyan" },
  };

  const requestStatusMap = {
    [REQUEST_STATUS.PENDING]: { text: "Đang chờ xử lý", color: "orange" },
    [REQUEST_STATUS.APPROVED]: { text: "Đã chấp nhận", color: "green" },
    [REQUEST_STATUS.PROCESSING]: { text: "Đang xử lý", color: "blue" },
    [REQUEST_STATUS.FULFILLED]: { text: "Hoàn thành", color: "green" },
    [REQUEST_STATUS.REJECTED]: { text: "Từ chối", color: "red" },
    [REQUEST_STATUS.CANCELLED]: { text: "Đã hủy", color: "red" },
    [REQUEST_STATUS.COMPLETED]: { text: "Hoàn thành", color: "green" },
  };

  if (type === "donation") {
    return (
      donationStatusMap[status] || {
        text: "Không xác định",
        color: "default",
      }
    );
  } else {
    return (
      requestStatusMap[status] || { text: "Không xác định", color: "default" }
    );
  }
};
